// Divi Layout Library Styles
// Following BEM naming convention and WordPress admin styling

// Variables
$primary-color: #0073aa;
$secondary-color: #00a0d2;
$success-color: #46b450;
$error-color: #dc3232;
$warning-color: #ffb900;
$text-color: #23282d;
$border-color: #ddd;
$background-color: #f1f1f1;
$white: #fff;
$gray-light: #f9f9f9;
$gray-medium: #e5e5e5;
$gray-dark: #666;

// Mixins
@mixin button-style($bg-color, $text-color: $white) {
	background-color: $bg-color;
	color: $text-color;
	border: 1px solid darken($bg-color, 10%);
	padding: 8px 16px;
	border-radius: 3px;
	cursor: pointer;
	text-decoration: none;
	display: inline-flex;
	align-items: center;
	gap: 5px;
	font-size: 13px;
	line-height: 1.4;
	transition: all 0.2s ease;

	&:hover {
		background-color: darken($bg-color, 5%);
		border-color: darken($bg-color, 15%);
		color: $text-color;
	}

	&:focus {
		box-shadow:
			0 0 0 1px $white,
			0 0 0 3px $bg-color;
		outline: none;
	}

	&:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}
}

@mixin card-shadow {
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

@mixin loading-spinner {
	border: 2px solid $gray-medium;
	border-top: 2px solid $primary-color;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

// Main App Container
.dll-app {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
		Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	color: $text-color;
	line-height: 1.4;
}

// Dashboard
.dll-dashboard {
	padding: 20px;

	&--loading,
	&--error {
		display: flex;
		align-items: center;
		justify-content: center;
		min-height: 400px;
	}

	&__header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30px;
		padding-bottom: 20px;
		border-bottom: 1px solid $border-color;
	}

	&__title {
		margin: 0;
		font-size: 24px;
		font-weight: 600;
		color: $text-color;
	}

	&__toolbar {
		display: flex;
		align-items: center;
		gap: 15px;
	}

	&__content {
		display: flex;
		gap: 30px;
	}

	&__main {
		flex: 1;
	}
}

// View Toggle
.dll-view-toggle {
	display: flex;
	border-radius: 3px;
	overflow: hidden;

	&__button {
		background: $white;
		border: none;
		padding: 8px 12px;
		cursor: pointer;
		color: $gray-dark;
		transition: all 0.2s ease;

		&--active {
			background-color: $primary-color;
			color: $white;
		}

		.dashicons {
			font-size: 16px;
		}
	}
}

// Buttons
.dll-button {
	@include button-style($primary-color);

	&--secondary {
		@include button-style($white, $text-color);
	}

	&.button-secondary {
		background-color: $white;
		border-color: transparent;
	}
	&--small {
		padding: 6px 12px;
		font-size: 12px;
	}

	.dashicons {
		font-size: 14px;
		line-height: 1.4rem;
	}
}

// Loading
.dll-loading {
	text-align: center;

	&__spinner {
		@include loading-spinner;
		width: 40px;
		height: 40px;
		margin: 0 auto 15px;

		&--small {
			width: 16px;
			height: 16px;
			border-width: 1px;
		}
	}
}

// Error
.dll-error {
	text-align: center;
	padding: 40px 20px;

	h3 {
		color: $error-color;
		margin-bottom: 10px;
	}

	p {
		margin-bottom: 20px;
		color: $gray-dark;
	}
}

// Sidebar
.dll-sidebar {
	width: 250px;
	flex-shrink: 0;

	&__header {
		margin-bottom: 20px;
	}

	&__search {
		margin-bottom: 10px;
	}

	&__title {
		font-size: 16px;
		font-weight: 600;
		margin: 0;
		display: flex;
		align-items: center;
		gap: 8px;
		color: $text-color;

		.dashicons {
			color: $primary-color;
		}
	}
}

// Category List
.dll-category-list {
	list-style: none;
	margin: 0;
	padding: 0;

	&__item {
		margin-bottom: 2px;
	}

	&__button {
		width: 100%;
		background: none;
		border: none;
		padding: 10px 15px;
		text-align: left;
		cursor: pointer;
		border-radius: 3px;
		transition: all 0.2s ease;
		display: flex;
		justify-content: space-between;
		align-items: center;

		&:hover {
			background-color: $gray-light;
		}

		&--active {
			background-color: $primary-color !important;
			color: $white;
		}
	}

	&__name {
		font-weight: 500;
	}

	&__count {
		background-color: $gray-medium;
		color: $gray-dark;
		padding: 2px 6px;
		border-radius: 10px;
		font-size: 11px;
		font-weight: 600;

		.dll-category-list__button--active & {
			background-color: rgba(255, 255, 255, 0.2);
			color: $white;
		}
	}
}

// Layouts Grid/List
.dll-layouts {
	&--grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
		gap: 20px;
	}

	&--list {
		display: flex;
		flex-direction: column;
		gap: 15px;
	}

	&__empty {
		text-align: center;
		padding: 60px 20px;
		color: $gray-dark;
		grid-column: 1 / -1;
	}
}

// Layout Card
.dll-layout-card {
	background: $white;
	border: 1px solid $border-color;
	border-radius: 6px;
	overflow: hidden;
	transition: all 0.3s ease;
	@include card-shadow;

	&:hover {
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	}

	&--grid {
		.dll-layout-card__image-container {
			height: 200px;
			position: relative;
			overflow: hidden;
		}

		.dll-layout-card__content {
			padding: 15px;
		}
	}

	&--list {
		display: flex;
		align-items: center;

		.dll-layout-card__image-wrapper {
			width: 150px;
			flex-shrink: 0;
		}

		.dll-layout-card__image-container {
			height: 100px;
			position: relative;
			overflow: hidden;
		}

		.dll-layout-card__content-wrapper {
			flex: 1;
			padding: 15px;
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.dll-layout-card__actions-list {
			margin-left: 20px;
		}
	}

	&__image {
		width: 100%;
		height: 100%;
		object-fit: cover;
		transition: transform 0.3s ease;

		&--scrolling {
			transform: translateY(-20%);
		}
	}

	&__image-placeholder,
	&__image-error {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
		background-color: $gray-light;
		color: $gray-dark;
		flex-direction: column;
		gap: 10px;

		.dashicons {
			font-size: 24px;
		}
	}

	&__overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.7);
		display: flex;
		align-items: center;
		justify-content: center;
		opacity: 0;
		transition: opacity 0.3s ease;

		.dll-layout-card:hover & {
			opacity: 1;
		}
	}

	&__actions {
		display: flex;
		gap: 10px;

		> button.is-secondary {
			background: $white;

			&:hover {
				background: $white !important;
			}
		}
	}

	&__title {
		font-size: 16px;
		font-weight: 600;
		margin: 0 0 5px 0;
		color: $text-color;
	}

	&__category {
		font-size: 12px;
		color: $primary-color;
		font-weight: 500;
		margin: 0 0 8px 0;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	&__description {
		font-size: 13px;
		color: $gray-dark;
		margin: 0;
		line-height: 1.4;
	}
}

// Modal
.dll-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 100000;
	padding: 20px;
}

.dll-modal {
	background: $white;
	border-radius: 6px;
	max-width: 600px;
	width: 100%;
	max-height: 90vh;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	@include card-shadow;

	&__header {
		padding: 20px;
		border-bottom: 1px solid $border-color;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: $gray-light;
	}

	&__title {
		margin: 0;
		font-size: 18px;
		font-weight: 600;
		display: flex;
		align-items: center;
		gap: 8px;

		.dashicons {
			color: $primary-color;
		}
	}

	&__close {
		background: none;
		border: none;
		padding: 5px;
		cursor: pointer;
		border-radius: 3px;
		color: $gray-dark;

		&:hover {
			background-color: $gray-medium;
		}

		.dashicons {
			font-size: 18px;
		}
	}

	&__content {
		padding: 20px;
		overflow-y: auto;
		flex: 1;
	}

	&__footer {
		padding: 20px;
		border-top: 1px solid $border-color;
		display: flex;
		justify-content: flex-end;
		gap: 10px;
		background: $gray-light;
	}
}

// Forms
.dll-form-group {
	margin-bottom: 20px;
}

.dll-form-label {
	display: block;
	margin-bottom: 5px;
	font-weight: 500;
	color: $text-color;
}

.dll-form-input,
.dll-form-select {
	width: 100%;
	max-width: 100% !important;
	padding: 8px 12px;
	border: 1px solid $border-color;
	border-radius: 3px;
	font-size: 14px;
	transition: border-color 0.2s ease;

	&:focus {
		outline: none;
		border-color: $primary-color;
		box-shadow: 0 0 0 1px $primary-color;
	}
}

.dll-form-help {
	margin-top: 5px;
	font-size: 12px;
	color: $gray-dark;
}

// Radio Options
.dll-radio-option {
	display: block;
	margin-bottom: 15px;
	cursor: pointer;

	input[type="radio"] {
		margin-right: 10px;
	}

	&__label {
		display: flex;
		align-items: flex-start;
		gap: 10px;
	}
}

// Import Modal Specific
.dll-import-modal {
	.dll-import-options {
		margin-bottom: 25px;

		h3 {
			margin-bottom: 15px;
			font-size: 16px;
		}
	}

	.dll-page-options {
		background: $gray-light;
		border-radius: 3px;
		margin-top: 15px;
	}
}

.dll-import-progress {
	text-align: center;
	padding: 40px 20px;

	&__text {
		margin-bottom: 20px;
		font-size: 16px;
		color: $text-color;
	}
}

.dll-progress {
	margin-bottom: 15px;

	&__bar {
		width: 100%;
		height: 8px;
		background: $gray-medium;
		border-radius: 4px;
		overflow: hidden;
	}

	&__fill {
		height: 100%;
		background: linear-gradient(90deg, $primary-color, $secondary-color);
		transition: width 0.3s ease;
		border-radius: 4px;
	}

	&__text {
		margin-top: 10px;
		font-size: 14px;
		font-weight: 500;
		color: $text-color;
	}
}

.dll-import-success {
	text-align: center;
	padding: 40px 20px;
	position: relative;

	&__icon {
		font-size: 48px;
		color: $success-color;
		margin-bottom: 15px;

		.dashicons {
			font-size: 48px;
		}
	}

	&__title {
		font-size: 20px;
		margin-bottom: 10px;
		color: $text-color;
	}

	&__message {
		margin-bottom: 25px;
		color: $gray-dark;
	}

	&__actions {
		display: flex;
		justify-content: center;
		gap: 10px;
	}
}

.dll-import-error {
	text-align: center;
	padding: 40px 20px;

	&__icon {
		font-size: 48px;
		margin-bottom: 15px;
	}

	&__title {
		font-size: 18px;
		color: $error-color;
		margin-bottom: 10px;
	}

	&__message {
		margin-bottom: 25px;
		color: $gray-dark;
	}
}

// Export Modal Specific
.dll-export-modal {
	max-width: 700px;
}

.dll-export-step {
	margin-bottom: 30px;

	h3 {
		margin-bottom: 15px;
		font-size: 16px;
		color: $text-color;
	}
}

.dll-layout-list {
	&__empty {
		text-align: center;
		padding: 40px 20px;
		color: $gray-dark;
	}

	&__items {
		max-height: 300px;
		overflow-y: auto;
		border: 1px solid $border-color;
		border-radius: 3px;
	}
}

.dll-layout-item {
	display: flex;
	align-items: center;
	padding: 15px;
	border-bottom: 1px solid $border-color;
	cursor: pointer;
	transition: background-color 0.2s ease;

	&:last-child {
		border-bottom: none;
	}

	&:hover {
		background-color: $gray-light;
	}

	&--selected {
		background-color: lighten($primary-color, 45%);
		border-color: $primary-color;
	}

	&__content {
		flex: 1;
	}

	&__title {
		margin: 0 0 5px 0;
		font-size: 14px;
		font-weight: 600;
		color: $text-color;
	}

	&__meta {
		display: flex;
		gap: 15px;
		font-size: 12px;
		color: $gray-dark;
	}

	&__type,
	&__status {
		text-transform: capitalize;
	}

	&__actions {
		margin-left: 15px;
	}

	&__edit {
		color: $gray-dark;
		text-decoration: none;
		padding: 5px;
		border-radius: 3px;
		transition: background-color 0.2s ease;

		&:hover {
			background-color: $gray-medium;
			color: $primary-color;
		}
	}
}

.dll-export-success {
	text-align: center;
	padding: 40px 20px;

	&__icon {
		font-size: 48px;
		color: $success-color;
		margin-bottom: 15px;

		.dashicons {
			font-size: 48px;
		}
	}

	h3 {
		color: $success-color;
		margin-bottom: 10px;
	}
}

.dll-export-error-inline {
	background-color: lighten($error-color, 45%);
	color: $error-color;
	padding: 10px 15px;
	border-radius: 3px;
	margin-top: 15px;
	display: flex;
	align-items: center;
	gap: 8px;

	.dashicons {
		flex-shrink: 0;
	}
}

// Confetti Animation
.dll-confetti {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
	overflow: hidden;

	&__piece {
		position: absolute;
		width: 8px;
		height: 8px;
		top: -10px;
		animation: confetti-fall 3s linear infinite;
	}
}

@keyframes confetti-fall {
	0% {
		transform: translateY(-100vh) rotate(0deg);
		opacity: 1;
	}
	100% {
		transform: translateY(100vh) rotate(360deg);
		opacity: 0;
	}
}

// Responsive Design
@media (max-width: 768px) {
	.dll-dashboard {
		padding: 15px;

		&__header {
			flex-direction: column;
			align-items: flex-start;
			gap: 15px;
		}

		&__toolbar {
			width: 100%;
			justify-content: space-between;
		}

		&__content {
			flex-direction: column;
			gap: 20px;
		}
	}

	.dll-sidebar {
		width: 100%;
	}

	.dll-layouts {
		&--grid {
			grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
			gap: 15px;
		}
	}

	.dll-layout-card {
		&--list {
			flex-direction: column;

			.dll-layout-card__image-wrapper {
				width: 100%;
			}

			.dll-layout-card__content {
				flex-direction: column;
				align-items: flex-start;
				gap: 15px;
			}

			.dll-layout-card__actions-list {
				margin-left: 0;
			}
		}
	}

	.dll-modal {
		margin: 10px;
		max-height: calc(100vh - 20px);

		&__content {
			padding: 15px;
		}

		&__footer {
			padding: 15px;
			flex-direction: column;
			gap: 10px;

			.dll-button {
				width: 100%;
				justify-content: center;
			}
		}
	}

	.dll-import-success__actions {
		flex-direction: column;
		gap: 10px;

		.dll-button {
			width: 100%;
			justify-content: center;
		}
	}
}
