export const predefinedLayouts = [
	{
		id: "layout-1",
		name: "Modern Business",
		category: "Business",
		description: "A clean and modern business layout for corporate sites.",
		previewImage: "",
		previewLink: "https://demo.example.com/business-modern",
		jsonFile:
			"https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/Web-Services.json",
	},
	{
		id: "layout-2",
		name: "Creative Portfolio",
		category: "Portfolio",
		description: "A creative portfolio layout perfect for showcasing work.",
		previewImage: "",
		previewLink: "https://demo.example.com/portfolio-creative",
		jsonFile:
			"https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/portfolio-creative.json",
	},
	{
		id: "layout-3",
		name: "Restaurant Menu",
		category: "Restaurant",
		description: "An elegant restaurant layout with menu showcase.",
		previewImage: "",
		previewLink: "https://demo.example.com/restaurant-menu",
		jsonFile:
			"https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/restaurant-menu.json",
	},
	{
		id: "layout-4",
		name: "Tech Startup",
		category: "Business",
		description: "A modern tech startup layout with bold design.",
		previewImage: "",
		previewLink: "https://demo.example.com/tech-startup",
		jsonFile:
			"https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/blurbcore.json",
	},
	{
		id: "layout-5",
		name: "Photography Studio",
		category: "Portfolio",
		description: "A stunning photography portfolio layout.",
		previewImage: "",
		previewLink: "https://demo.example.com/photography-studio",
		jsonFile:
			"/wp-content/plugins/divi-layout-library/assets/layouts/photography-studio.json",
	},
	{
		id: "layout-6",
		name: "Coffee Shop",
		category: "Restaurant",
		description: "A cozy coffee shop layout with warm colors.",
		previewImage: "",
		previewLink: "https://demo.example.com/coffee-shop",
		jsonFile:
			"/wp-content/plugins/divi-layout-library/assets/layouts/coffee-shop.json",
	},
	{
		id: "layout-7",
		name: "Gardener Shop",
		category: "Garden",
		description: "A garden shop.",
		previewImage: "",
		previewLink: "https://demo.example.com/coffee-shop",
		jsonFile:
			"https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/Gardener-All-Layouts-Import.json",
	},
	{
		id: "layout-8",
		name: "Divi Person Layout",
		category: "Garden",
		description: "A garden shop.",
		previewImage: "",
		previewLink: "https://demo.example.com/coffee-shop",
		jsonFile:
			"https://raw.githubusercontent.com/hrrarya/layouts/refs/heads/main/person-layout.json",
	},
	{
		id: "layout-9",
		name: "Barber Website",
		category: "Barber",
		description: "A barber website.",
		previewImage: "",
		previewLink: "",
		jsonFile:
			"https://diviessential.com/wp-content/uploads/2022/08/Barber.json",
	},
];
