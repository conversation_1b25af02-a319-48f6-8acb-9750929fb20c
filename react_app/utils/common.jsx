import { Button, Dashicon } from "@wordpress/components";
import { __ } from "@wordpress/i18n";
/**
 * Render card actions
 */
export const RenderActions = ({ onImport, onPreview }) => (
	<div className="dll-layout-card__actions">
		<Button
			variant="primary"
			onClick={onImport}
			icon={<Dashicon icon="download" size={16} />}
			text={__("Import", "divi-layout-library")}
		/>
		<Button
			variant="secondary"
			className=""
			onClick={onPreview}
			icon={<Dashicon icon="visibility" size={16} />}
			text={__("Preview", "divi-layout-library")}
		/>
	</div>
);

/**
 * Render progress bar
 */

export const Progressbar = ({ progress }) => (
	<div className="dll-progress">
		<div className="dll-progress__bar">
			<div
				className="dll-progress__fill"
				style={{ width: `${progress}%` }}
			></div>
		</div>
		<div className="dll-progress__text">
			{progress < 100
				? `${Math.round(progress)}%`
				: __("Complete!", "divi-layout-library")}
		</div>
	</div>
);

export const Confetti = () => (
	<div className="dll-confetti">
		{Array.from({ length: 50 }).map((_, i) => (
			<div
				key={i}
				className="dll-confetti__piece"
				style={{
					left: `${Math.random() * 100}%`,
					animationDelay: `${Math.random() * 3}s`,
					backgroundColor: [
						"#ff6b6b",
						"#4ecdc4",
						"#45b7d1",
						"#96ceb4",
						"#feca57",
					][Math.floor(Math.random() * 5)],
				}}
			></div>
		))}
	</div>
);
