import { useState, useEffect } from "react";
import { __ } from "@wordpress/i18n";
import { Button, Dashicon } from "@wordpress/components";
import ApiService from "@services/ApiService";

const PreviewModal = ({ layout, onClose, onImport }) => {
	const [pageName, setPageName] = useState("");
	const [pageStatus, setPageStatus] = useState("draft");
	const [showCreatePageForm, setShowCreatePageForm] = useState(false);
	const [isCreatingPage, setIsCreatingPage] = useState(false);
	const [error, setError] = useState(null);
	const [success, setSuccess] = useState(null);

	const apiService = new ApiService();

	useEffect(() => {
		if (layout?.name) {
			setPageName(layout.name);
		}
	}, [layout]);

	/**
	 * Handle modal close
	 */
	const handleClose = () => {
		if (!isCreatingPage) {
			onClose();
		}
	};

	/**
	 * Handle import layout
	 */
	const handleImport = () => {
		onImport();
		onClose();
	};

	/**
	 * Handle create page
	 */
	const handleCreatePage = async () => {
		if (!pageName.trim()) {
			setError(__("Page name is required", "divi-layout-library"));
			return;
		}

		setIsCreatingPage(true);
		setError(null);

		try {
			// Load layout file from URL and convert to File object
			const response = await fetch(layout.jsonFile);
			if (!response.ok) {
				throw new Error(
					__("Failed to load layout file", "divi-layout-library"),
				);
			}

			const jsonContent = await response.text();
			const fileName = layout.jsonFile.split("/").pop() || "layout.json";
			const file = new File([jsonContent], fileName, {
				type: "application/json",
			});

			// Import using Divi's native system with page creation
			const importOptions = {
				includeGlobalPresets: false,
				createPage: true,
				pageTitle: pageName.trim(),
				pageStatus: pageStatus,
			};

			const result = await apiService.importLayout(file, importOptions);

			if (result?.success) {
				setSuccess(
					`Page "${pageName}" has been created successfully! <a href="${result.data?.data?.view_url}" target="_blank">View Page</a> | <a href="${result.data?.data?.edit_url}" target="_blank">Edit Page</a>`,
				);
			} else {
				throw new Error(__("Failed to create page", "divi-layout-library"));
			}
		} catch (err) {
			setError(
				err.message || __("Failed to create page", "divi-layout-library"),
			);
		} finally {
			setIsCreatingPage(false);
		}
	};

	/**
	 * Handle open in new tab
	 */
	const handleOpenInNewTab = () => {
		if (layout?.previewLink) {
			window.open(layout.previewLink, "_blank");
		}
	};

	/**
	 * Toggle create page form
	 */
	const toggleCreatePageForm = () => {
		setShowCreatePageForm(!showCreatePageForm);
		setError(null);
	};

	return (
		<div className="dll-preview-modal-overlay" onClick={handleClose}>
			<div className="dll-preview-modal" onClick={(e) => e.stopPropagation()}>
				{/* Header */}
				<div className="dll-preview-modal__header">
					<div className="dll-preview-modal__header-left">
						<h2 className="dll-preview-modal__title">
							{layout?.name || __("Layout Preview", "divi-layout-library")}
						</h2>
					</div>

					<div className="dll-preview-modal__header-center">
						<Button
							variant="primary"
							onClick={handleImport}
							icon={<Dashicon icon="download" size={16} />}
							text={__("Import", "divi-layout-library")}
						/>

						<div className="dll-preview-modal__create-page">
							<Button
								variant="secondary"
								onClick={toggleCreatePageForm}
								icon={<Dashicon icon="plus-alt" size={16} />}
								text={__("Create Page", "divi-layout-library")}
							/>

							{showCreatePageForm && (
								<div className="dll-preview-modal__create-page-form">
									<div className="dll-form-group">
										<input
											type="text"
											className="dll-form-input"
											placeholder={__("Enter page name", "divi-layout-library")}
											value={pageName}
											onChange={(e) => setPageName(e.target.value)}
											disabled={isCreatingPage}
										/>
									</div>
									<div className="dll-form-group">
										<select
											className="dll-form-select"
											value={pageStatus}
											onChange={(e) => setPageStatus(e.target.value)}
											disabled={isCreatingPage}
										>
											<option value="draft">
												{__("Draft", "divi-layout-library")}
											</option>
											<option value="publish">
												{__("Published", "divi-layout-library")}
											</option>
											<option value="private">
												{__("Private", "divi-layout-library")}
											</option>
										</select>
									</div>
									{error && <div className="dll-error-message">{error}</div>}
									{success && (
										<div
											className="dll-success-message"
											dangerouslySetInnerHTML={{ __html: success }}
										/>
									)}
									<div className="dll-form-actions">
										<Button
											variant="primary"
											onClick={handleCreatePage}
											disabled={isCreatingPage}
											text={
												isCreatingPage
													? __("Creating...", "divi-layout-library")
													: __("Create", "divi-layout-library")
											}
										/>
										<Button
											variant="secondary"
											onClick={toggleCreatePageForm}
											disabled={isCreatingPage}
											text={__("Cancel", "divi-layout-library")}
										/>
									</div>
								</div>
							)}
						</div>
					</div>

					<div className="dll-preview-modal__header-right">
						{layout?.previewLink && (
							<Button
								variant="secondary"
								onClick={handleOpenInNewTab}
								icon={<Dashicon icon="external" size={16} />}
								text={__("Open in New Tab", "divi-layout-library")}
							/>
						)}

						<button
							className="dll-preview-modal__close"
							onClick={handleClose}
							disabled={isCreatingPage}
						>
							<Dashicon icon="no-alt" size={20} />
						</button>
					</div>
				</div>

				{/* Content */}
				<div className="dll-preview-modal__content">
					{layout?.previewLink ? (
						<iframe
							src={layout.previewLink}
							className="dll-preview-modal__iframe"
							title={
								layout?.name || __("Layout Preview", "divi-layout-library")
							}
							frameBorder="0"
							allowFullScreen
						/>
					) : (
						<div className="dll-preview-modal__no-preview">
							<div className="dll-preview-modal__no-preview-icon">
								<Dashicon icon="format-image" size={48} />
							</div>
							<h3>{__("Preview Not Available", "divi-layout-library")}</h3>
							<p>
								{__(
									"This layout doesn't have a preview URL available.",
									"divi-layout-library",
								)}
							</p>
						</div>
					)}
				</div>
			</div>
		</div>
	);
};

export default PreviewModal;
