import { useState } from "react";
import ApiService from "@services/ApiService";
import { __ } from "@wordpress/i18n";
import { Button, Dashicon } from "@wordpress/components";
import { Progressbar, Confetti } from "@utils/common";
import ImportForm from "@components/importModal/import-form";

const ImportModal = ({ layout, onClose }) => {
	const [importType, setImportType] = useState("library"); // 'library' or 'page'
	const [pageName, setPageName] = useState("");
	const [pageStatus, setPageStatus] = useState("draft");
	const [isImporting, setIsImporting] = useState(false);
	const [progress, setProgress] = useState(0);
	const [importResult, setImportResult] = useState(null);
	const [error, setError] = useState(null);
	const [showConfetti, setShowConfetti] = useState(false);

	const apiService = new ApiService();

	/**
	 * Handle import type change
	 */
	const handleImportTypeChange = (type) => {
		setImportType(type);
		setError(null);

		// Set default page name when switching to page creation
		if (type === "page" && !pageName) {
			setPageName(layout?.name || "");
		}
	};

	/**
	 * Validate form inputs
	 */
	const validateInputs = () => {
		if (importType === "page") {
			if (!pageName.trim()) {
				setError(
					apiService.getString("pageNameRequired") ||
						__("Page name is required", "divi-layout-library"),
				);
				return false;
			}
		}
		return true;
	};

	/**
	 * Simulate progress for better UX
	 */
	const simulateProgress = () => {
		setProgress(0);
		const interval = setInterval(() => {
			setProgress((prev) => {
				if (prev >= 90) {
					clearInterval(interval);
					return 90;
				}
				return prev + Math.random() * 20;
			});
		}, 200);
		return interval;
	};

	/**
	 * Handle import process
	 */
	const handleImport = async () => {
		if (!validateInputs()) {
			return;
		}

		setIsImporting(true);
		setError(null);
		setImportResult(null);

		const progressInterval = simulateProgress();

		try {
			// Load layout file from URL and convert to File object
			const response = await fetch(layout.jsonFile);
			if (!response.ok) {
				throw new Error(
					__("Failed to load layout file", "divi-layout-library"),
				);
			}

			const jsonContent = await response.text();
			const fileName = layout.jsonFile.split("/").pop() || "layout.json";
			const file = new File([jsonContent], fileName, {
				type: "application/json",
			});

			// Import using Divi's native system
			const importOptions = {
				includeGlobalPresets: false,
				createPage: importType === "page",
				pageTitle: importType === "page" ? pageName.trim() : undefined,
				pageStatus: pageStatus,
			};

			const result = await apiService.importLayout(file, importOptions);

			// Complete progress
			clearInterval(progressInterval);
			setProgress(100);

			// Verify the import was successful
			if (result.success) {
				const verification = await apiService.verifyImportSuccess(result);
				console.log("Verification result:", verification);

				result.verification = verification;
			}

			if (!result?.success) {
				throw new Error("Import failed");
			}
			setImportResult(result?.data);
			setShowConfetti(true);

			setTimeout(() => {
				setShowConfetti(false);
			}, 3000);
		} catch (err) {
			clearInterval(progressInterval);
			setError(err.message || apiService.getString("error"));
			setProgress(0);
		} finally {
			setIsImporting(false);
		}
	};

	/**
	 * Handle modal close
	 */
	const handleClose = () => {
		if (!isImporting) {
			onClose();
		}
	};

	/**
	 * Render success state
	 */
	const renderSuccess = () => (
		<div className="dll-import-success">
			{showConfetti && <Confetti />}
			<div className="dll-import-success__content">
				<div className="dll-import-success__icon">
					<span className="dashicons dashicons-yes-alt" />
				</div>
				<h3 className="dll-import-success__title">
					{__("Congratulations!", "divi-layout-library")} 🎉
				</h3>
				<p className="dll-import-success__message">
					{importType === "page"
						? `Page "${pageName}" has been created successfully!`
						: __(
								"Layout has been imported to your library successfully!",
								"divi-layout-library",
						  )}
				</p>

				{importResult?.data?.edit_url && (
					<div className="dll-import-success__actions">
						<a
							href={importResult.data.edit_url}
							className="dll-button dll-button--primary"
							target="_blank"
							rel="noopener noreferrer"
						>
							{__("Edit Page", "divi-layout-library")}
						</a>
						{importResult.data?.view_url && (
							<a
								href={importResult.data.view_url}
								className="dll-button dll-button--secondary"
								target="_blank"
								rel="noopener noreferrer"
							>
								{__("View Page", "divi-layout-library")}
							</a>
						)}
					</div>
				)}
			</div>
		</div>
	);

	/**
	 * Render error state
	 */
	const renderError = () => (
		<div className="dll-import-error">
			<div className="dll-import-error__icon">
				<span>😞</span>
			</div>
			<h3 className="dll-import-error__title">
				{__("Oops! Import Failed.", "divi-layout-library")}
			</h3>
			<p className="dll-import-error__message">{error}</p>
			<button
				className="dll-button dll-button--primary"
				onClick={() => {
					setError(null);
					setProgress(0);
				}}
			>
				{__("Try Again", "divi-layout-library")}
			</button>
		</div>
	);

	return (
		<div className="dll-modal-overlay" onClick={handleClose}>
			<div
				className="dll-modal dll-import-modal"
				onClick={(e) => e.stopPropagation()}
			>
				<div className="dll-modal__header">
					<h2 className="dll-modal__title">
						{__("Import Layout", "divi-layout-library")} {layout?.name}
					</h2>
					<button
						className="dll-modal__close"
						onClick={handleClose}
						disabled={isImporting}
					>
						<span className="dashicons dashicons-no-alt" />
					</button>
				</div>

				<div className="dll-modal__content">
					{error ? (
						renderError()
					) : importResult ? (
						renderSuccess()
					) : (
						<>
							{isImporting ? (
								<div className="dll-import-progress">
									<p className="dll-import-progress__text">
										{apiService.getString("importing")}
									</p>
									<Progressbar progress={progress} />
								</div>
							) : (
								<ImportForm
									importType={importType}
									handleImportType={handleImportTypeChange}
									pageName={pageName}
									setPageName={setPageName}
									pageStatus={pageStatus}
									setPageStatus={setPageStatus}
								/>
							)}
						</>
					)}
				</div>

				{!isImporting && !importResult && !error && (
					<div className="dll-modal__footer">
						<Button
							variant="secondary"
							onClick={handleClose}
							text={__("Cancel", "divi-layout-library")}
						/>
						<Button
							variant="primary"
							onClick={handleImport}
							icon={<Dashicon icon="download" size={16} />}
							text={__("Import Layout", "divi-layout-library")}
						/>
					</div>
				)}
			</div>
		</div>
	);
};

export default ImportModal;
