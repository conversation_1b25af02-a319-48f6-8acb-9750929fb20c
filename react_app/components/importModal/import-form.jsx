import { __ } from "@wordpress/i18n";
import {
	RadioControl,
	SelectControl,
	__experimentalInputControl as InputControl,
} from "@wordpress/components";

const ImportForm = ({
	importType,
	handleImportType,
	pageName,
	setPageName,
	pageStatus,
	setPageStatus,
}) => {
	return (
		<div className="dll-import-form">
			<div className="dll-import-options">
				<h3>{__("Import Options", "divi-layout-library")}</h3>

				<RadioControl
					selected={importType}
					options={[
						{
							label: (
								<>
									<strong>
										{__("Make a New Page", "divi-layout-library")}
									</strong>{" "}
									{__("with this layout.", "divi-layout-library")}
								</>
							),
							value: "page",
						},
						{
							label: (
								<>
									<strong>
										{__("Just Import Layout", "divi-layout-library")}
									</strong>{" "}
									{__("(add to Divi library.)", "divi-layout-library")}
								</>
							),
							value: "library",
						},
					]}
					onChange={(value) => handleImportType(value)}
				/>
			</div>

			{importType === "page" && (
				<div className="dll-page-options">
					<div className="dll-form-group">
						<InputControl
							__next40pxDefaultSize
							value={pageName}
							onChange={(nextValue) => setPageName(nextValue ?? "")}
							label={__("Page Name *", "divi-layout-library")}
							required
						/>
					</div>

					<div className="dll-form-group">
						<SelectControl
							__next40pxDefaultSize
							__nextHasNoMarginBottom
							label={__("Page Status", "divi-layout-library")}
							value={pageStatus}
							options={[
								{ label: __("Draft", "divi-layout-library"), value: "draft" },
								{
									label: __("Publish", "divi-layout-library"),
									value: "publish",
								},
								{
									label: __("Private", "divi-layout-library"),
									value: "private",
								},
							]}
							onChange={(status) => setPageStatus(status)}
						/>
					</div>
				</div>
			)}
		</div>
	);
};

export default ImportForm;
