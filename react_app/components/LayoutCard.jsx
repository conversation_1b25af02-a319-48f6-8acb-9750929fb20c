import { useState } from "react";
import { __ } from "@wordpress/i18n";
import { RenderActions } from "@utils/common";

const LayoutCard = ({ layout, viewMode, onImport, onPreview }) => {
	const [imageLoaded, setImageLoaded] = useState(false);
	const [imageError, setImageError] = useState(false);
	const isHovered = false;

	/**
	 * Handle image load success
	 */
	const handleImageLoad = () => {
		setImageLoaded(true);
	};

	/**
	 * Handle image load error
	 */
	const handleImageError = () => {
		setImageError(true);
		setImageLoaded(true);
	};

	/**
	 * Handle import button click
	 */
	const handleImportClick = (e) => {
		e.preventDefault();
		e.stopPropagation();
		onImport();
	};

	/**
	 * Handle preview button click
	 */
	const handlePreviewClick = (e) => {
		e.preventDefault();
		e.stopPropagation();
		onPreview();
	};

	/**
	 * Render preview image
	 */
	const renderPreviewImage = () => (
		<div className="dll-layout-card__image-container">
			{!imageLoaded && !imageError && (
				<div className="dll-layout-card__image-placeholder">
					<div className="dll-loading__spinner dll-loading__spinner--small" />
				</div>
			)}

			{imageError ? (
				<div className="dll-layout-card__image-error">
					<span className="dashicons dashicons-format-image" />
					<span>{__("Image not available", "divi-layout-library")}</span>
				</div>
			) : (
				<img
					src={layout.previewImage}
					alt={layout.name}
					className={`dll-layout-card__image ${
						isHovered ? "dll-layout-card__image--scrolling" : ""
					}`}
					onLoad={handleImageLoad}
					onError={handleImageError}
					style={{ display: imageLoaded ? "block" : "none" }}
				/>
			)}

			{"grid" === viewMode && (
				<div className="dll-layout-card__overlay">
					<RenderActions
						onImport={handleImportClick}
						onPreview={handlePreviewClick}
					/>
				</div>
			)}
		</div>
	);

	/**
	 * Render card content
	 */
	const renderContent = () => (
		<>
			<div className="dll-layout-card__content">
				<h3 className="dll-layout-card__title">{layout.name}</h3>
				<p className="dll-layout-card__category">{layout.category}</p>
				<p className="dll-layout-card__description">{layout.description}</p>
			</div>
			{viewMode === "list" && (
				<div className="dll-layout-card__actions-list">
					<RenderActions
						onImport={handleImportClick}
						onPreview={handlePreviewClick}
					/>
				</div>
			)}
		</>
	);

	// Grid view layout
	if (viewMode === "grid") {
		return (
			<div className="dll-layout-card dll-layout-card--grid">
				{renderPreviewImage()}
				{renderContent()}
			</div>
		);
	}

	// List view layout
	return (
		<div className="dll-layout-card dll-layout-card--list">
			<div className="dll-layout-card__image-wrapper">
				{renderPreviewImage()}
			</div>
			<div className="dll-layout-card__content-wrapper">{renderContent()}</div>
		</div>
	);
};

export default LayoutCard;
