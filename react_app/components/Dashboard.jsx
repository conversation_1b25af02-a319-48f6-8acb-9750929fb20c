import { useState, useEffect } from "react";
import LayoutCard from "@components/LayoutCard";
import Sidebar from "@components/Sidebar";
import ImportModal from "@components/ImportModal";
import ExportModal from "@components/ExportModal";
import { __ } from "@wordpress/i18n";
import { predefinedLayouts } from "@utils/data";
import { Dashicon } from "@wordpress/components";
import { useDataContext } from "@contexts/DataContext";

const Dashboard = () => {
	const [layouts, setLayouts] = useState([]);
	const [filteredLayouts, setFilteredLayouts] = useState([]);
	const [selectedCategory, setSelectedCategory] = useState("all");
	const [viewMode, setViewMode] = useState("grid"); // 'grid' or 'list'
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState(null);
	const [showImportModal, setShowImportModal] = useState(false);
	const [showExportModal, setShowExportModal] = useState(false);
	const [selectedLayout, setSelectedLayout] = useState(null);
	const { search } = useDataContext();

	useEffect(() => {
		loadPredefinedLayouts();
	}, []);

	useEffect(() => {
		filterLayouts();
	}, [layouts, selectedCategory, search]);

	/**
	 * Load predefined layouts from static data
	 */
	const loadPredefinedLayouts = () => {
		try {
			setLoading(true);
			setLayouts(predefinedLayouts);
			setLoading(false);
		} catch (err) {
			setError("Failed to load layouts");
			setLoading(false);
		}
	};

	/**
	 * Filter layouts by selected category
	 */
	const filterLayouts = () => {
		const searchFilter = layouts.filter((layout) =>
			JSON.stringify(layout).toLowerCase().includes(search.toLowerCase()),
		);
		if (selectedCategory === "all") {
			setFilteredLayouts(searchFilter);
		} else {
			setFilteredLayouts(
				searchFilter.filter((layout) => layout.category === selectedCategory),
			);
		}
	};

	/**
	 * Get unique categories from layouts
	 */
	const getCategories = () => {
		const categories = ["all"];
		layouts.forEach((layout) => {
			if (!categories.includes(layout.category)) {
				categories.push(layout.category);
			}
		});
		return categories;
	};

	/**
	 * Handle layout import
	 */
	const handleImportLayout = (layout) => {
		setSelectedLayout(layout);
		setShowImportModal(true);
	};

	/**
	 * Handle layout preview
	 */
	const handlePreviewLayout = (layout) => {
		// if (layout.previewLink) {
		// 	window.open(layout.previewLink, "_blank");
		// }
	};

	if (loading) {
		return (
			<div className="dll-dashboard dll-dashboard--loading">
				<div className="dll-loading">
					<div className="dll-loading__spinner" />
					<p>{__("Loading layouts...", "divi-layout-library")}</p>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="dll-dashboard dll-dashboard--error">
				<div className="dll-error">
					<h3>{__("Error", "divi-layout-library")}</h3>
					<p>{error}</p>
					<button
						onClick={loadPredefinedLayouts}
						className="dll-button dll-button--primary"
					>
						{__("Try Again", "divi-layout-library")}
					</button>
				</div>
			</div>
		);
	}

	return (
		<div className="dll-dashboard">
			<div className="dll-dashboard__header">
				<h1 className="dll-dashboard__title">
					{__("Divi Layout Library", "divi-layout-library")}
				</h1>
				<div className="dll-dashboard__toolbar">
					<div className="dll-view-toggle">
						<button
							className={`dll-view-toggle__button ${
								viewMode === "grid" ? "dll-view-toggle__button--active" : ""
							}`}
							onClick={() => setViewMode("grid")}
							title={__("Grid View", "divi-layout-library")}
						>
							<span className="dashicons dashicons-grid-view" />
						</button>
						<button
							className={`dll-view-toggle__button ${
								viewMode === "list" ? "dll-view-toggle__button--active" : ""
							}`}
							onClick={() => setViewMode("list")}
							title={__("List View", "divi-layout-library")}
						>
							<Dashicon icon="list-view" />
						</button>
					</div>
				</div>
			</div>

			<div className="dll-dashboard__content">
				<Sidebar
					categories={getCategories()}
					selectedCategory={selectedCategory}
					onCategoryChange={setSelectedCategory}
				/>

				<div className="dll-dashboard__main">
					<div className={`dll-layouts dll-layouts--${viewMode}`}>
						{filteredLayouts.length === 0 ? (
							<div className="dll-layouts__empty">
								<p>
									{__(
										"No layouts found for the selected category.",
										"divi-layout-library",
									)}
								</p>
							</div>
						) : (
							filteredLayouts.map((layout) => (
								<LayoutCard
									key={layout.id}
									layout={layout}
									viewMode={viewMode}
									onImport={() => handleImportLayout(layout)}
									onPreview={() => handlePreviewLayout(layout)}
								/>
							))
						)}
					</div>
				</div>
			</div>

			{showImportModal && (
				<ImportModal
					layout={selectedLayout}
					onClose={() => {
						setShowImportModal(false);
						setSelectedLayout(null);
					}}
				/>
			)}

			{showExportModal && (
				<ExportModal onClose={() => setShowExportModal(false)} />
			)}
		</div>
	);
};

export default Dashboard;
