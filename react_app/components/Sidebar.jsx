import { __ } from "@wordpress/i18n";
import { useDataContext } from "@contexts/DataContext";
import { __experimentalInputControl as InputControl } from "@wordpress/components";

const Sidebar = ({ categories, selectedCategory, onCategoryChange }) => {
	const { search, setSearch } = useDataContext();

	/**
	 * Handle category click
	 */
	const handleCategoryClick = (category) => {
		onCategoryChange(category);
	};

	/**
	 * Get category display name
	 */
	const getCategoryDisplayName = (category) => {
		if (category === "all") {
			return "All Categories";
		}
		return category;
	};

	/**
	 * Get category count (placeholder for future implementation)
	 */
	const getCategoryCount = (category) => {
		return "";
	};

	return (
		<div className="dll-sidebar">
			<div className="dll-sidebar__header">
				<div className="dll-sidebar__search">
					<InputControl
						__next40pxDefaultSize
						placeholder={__("Search layout...", "divi-layout-library")}
						value={search}
						onChange={(nextValue) => setSearch(nextValue ?? "")}
					/>
				</div>
				<h3 className="dll-sidebar__title">
					<span className="dashicons dashicons-category" />
					{__("Categories", "divi-layout-library")}
				</h3>
			</div>

			<div className="dll-sidebar__content">
				<ul className="dll-category-list">
					{categories.map((category) => (
						<li key={category} className="dll-category-list__item">
							<button
								className={`dll-category-list__button ${
									selectedCategory === category
										? "dll-category-list__button--active"
										: ""
								}`}
								onClick={() => handleCategoryClick(category)}
							>
								<span className="dll-category-list__name">
									{getCategoryDisplayName(category)}
								</span>
								{getCategoryCount(category) && (
									<span className="dll-category-list__count">
										{getCategoryCount(category)}
									</span>
								)}
							</button>
						</li>
					))}
				</ul>
			</div>
		</div>
	);
};

export default Sidebar;
