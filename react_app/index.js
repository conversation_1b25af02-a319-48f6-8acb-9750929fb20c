import { createRoot } from "react-dom";
import App from "./App.jsx";
import { createHooks } from "@wordpress/hooks";
import { DataContextProvider } from "@contexts/DataContext.js";
import "./index.scss";

window.divi_layout_library_hooks = createHooks();

document.addEventListener("DOMContentLoaded", function () {
	const body = document.getElementById("divi-layout-library-body");
	const root = createRoot(body);

	root.render(
		<DataContextProvider>
			<App />
		</DataContextProvider>,
	);
});
