import { createContext, useContext, useState } from "react";
const DataContext = createContext();

function DataContextProvider({ children }) {
	const [search, setSearch] = useState("");
	const value = {
		search,
		setSearch,
	};

	return <DataContext.Provider value={value}>{children}</DataContext.Provider>;
}

function useDataContext() {
	return useContext(DataContext);
}

export { DataContextProvider, useDataContext };
