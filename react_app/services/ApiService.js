/**
 * API Service for handling AJAX requests to backend endpoints
 */
class ApiService {
	constructor() {
		this.ajaxUrl = window.dllAjax?.ajaxUrl || "/wp-admin/admin-ajax.php";
		this.nonce = window.dllAjax?.nonce || "";
		this.strings = window.dllAjax?.strings || {};
	}

	/**
	 * Make AJAX request to WordPress backend
	 *
	 * @param {string} action The WordPress AJAX action
	 * @param {Object} data Additional data to send
	 * @param {Object} options Request options
	 * @returns {Promise} Promise that resolves with response data
	 */
	async makeRequest(action, data = {}, options = {}) {
		const requestData = {
			action,
			nonce: this.nonce,
			...data,
		};

		const requestOptions = {
			method: "POST",
			headers: {
				"Content-Type": "application/x-www-form-urlencoded",
			},
			body: new URLSearchParams(requestData),
			...options,
		};

		try {
			const response = await fetch(this.ajaxUrl, requestOptions);

			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}

			const result = await response.json();

			console.info(result);

			if (!result?.success) {
				throw new Error(
					result.data?.message || this.strings.error || "Request failed",
				);
			}

			return result.data;
		} catch (error) {
			console.error("API Request failed:", error);
			throw error;
		}
	}

	/**
	 * Format builder layout file (similar to Divi's formatBuilderLayoutFile)
	 * Converts et_builder context to et_builder_layouts format
	 * @param {File} file - The JSON file to format
	 * @returns {Promise<File>} - Promise resolving to formatted file
	 */
	async formatBuilderLayoutFile(file) {
		const reader = new FileReader();

		return new Promise((resolve, reject) => {
			reader.onloadend = (e) => {
				let content = "";
				try {
					content = JSON.parse(e.target.result);
				} catch (e) {
					const importFile = new File([JSON.stringify({})], file.name, {
						type: "application/json",
					});
					return resolve(importFile);
				}

				if ("et_builder" === content.context) {
					const name = file.name.replace(".json", "");
					const postId = Object.keys(content.data)[0];
					const postContent = content.data[postId];

					const convertedFile = {
						...content,
						context: "et_builder_layouts",
						data: {
							[postId]: {
								ID: parseInt(postId, 10),
								post_title: name,
								post_name: name,
								post_content: postContent,
								post_excerpt: "",
								post_status: "publish",
								comment_status: "closed",
								ping_status: "closed",
								post_type: "et_pb_layout",
								post_meta: {
									_et_pb_built_for_post_type: ["page"],
								},
								terms: {
									1: {
										name: "layout",
										slug: "layout",
										taxonomy: "layout_type",
									},
								},
							},
						},
					};

					const importFile = new File(
						[JSON.stringify(convertedFile)],
						file.name,
						{ type: "application/json" },
					);
					resolve(importFile);
				} else {
					resolve(file);
				}
			};

			reader.onerror = () => {
				reader.abort();
				reject();
			};

			reader.readAsText(file);
		});
	}

	/**
	 * Import layout using Divi's native portability system
	 * @param {File} file - The JSON file to import
	 * @param {Object} options - Import options
	 * @returns {Promise} - Promise resolving to import result
	 */
	async importLayout(file, options = {}) {
		try {
			// Check if Divi's portability object is available
			if (!window.etCore || !window.etCore.portability) {
				console.warn(
					"Divi portability system not available, falling back to direct AJAX",
				);
				// Fallback to our previous jQuery implementation
				return this.importLayoutFallback(file, options);
			}

			// Format the file using Divi's logic
			const formattedFile = await this.formatBuilderLayoutFile(file);

			// Determine context from the formatted file
			const fileContent = await this.readFileAsText(formattedFile);
			let context = "et_builder_layouts"; // Default context

			try {
				const parsedContent = JSON.parse(fileContent);
				context = parsedContent.context || "et_builder_layouts";
			} catch (e) {
				// Use default context if parsing fails
			}

			console.log("Using Divi portability system with context:", context);
			console.log(
				"Available etCore.portability methods:",
				Object.keys(window.etCore.portability),
			);

			// Use Divi's native portability system
			return new Promise((resolve, reject) => {
				// Prepare data exactly like Divi's ajaxAction method
				const importData = {
					action: "et_core_portability_import",
					context: context,
					file: formattedFile,
					content: false,
					timestamp: 0,
					post: options.createPage ? 0 : jQuery("#post_ID").val() || 0,
					replace: options.createPage ? "0" : "0",
					include_global_presets: options.includeGlobalPresets ? "1" : "0",
					page: 1,
					nonce:
						window.etCorePortability?.nonces?.import ||
						window.dllAjax.portability_nonce,
				};

				console.log("Import data:", importData);

				// Use Divi's ajaxAction method directly
				window.etCore.portability.ajaxAction(
					importData,
					function (response) {
						console.log("Divi portability response:", response);

						// Handle page creation if requested
						if (options.createPage && response && response.data) {
							this.createPageWithLayout(response.data, options.pageTitle)
								.then(resolve)
								.catch(reject);
							return;
						}

						// Success response from Divi
						resolve({
							success: true,
							data: response.data || response,
							message: "Layout imported successfully",
						});
					}.bind(this),
					true,
				); // true for file support
			});
		} catch (error) {
			console.error("Import error:", error);
			return {
				success: false,
				message: error.message || "Import preparation failed",
			};
		}
	}

	/**
	 * Fallback import method using direct jQuery AJAX (if Divi's portability system isn't available)
	 * @param {File} file - The JSON file to import
	 * @param {Object} options - Import options
	 * @returns {Promise} - Promise resolving to import result
	 */
	async importLayoutFallback(file, options = {}) {
		try {
			// Format the file using Divi's logic
			const formattedFile = await this.formatBuilderLayoutFile(file);

			// Determine context from the formatted file
			const fileContent = await this.readFileAsText(formattedFile);
			let context = "et_builder_layouts"; // Default context

			try {
				const parsedContent = JSON.parse(fileContent);
				context = parsedContent.context || "et_builder_layouts";
			} catch (e) {
				// Use default context if parsing fails
			}

			console.log("Using fallback jQuery AJAX with context:", context);

			return new Promise((resolve, reject) => {
				const ajaxData = {
					action: "et_core_portability_import",
					context: context,
					nonce: window.dllAjax.portability_nonce,
					file: formattedFile,
					content: false,
					timestamp: 0,
					post: options.createPage ? 0 : jQuery("#post_ID").val() || 0,
					replace: options.createPage ? "0" : "0",
					include_global_presets: options.includeGlobalPresets ? "1" : "0",
					page: 1,
				};

				const formData = new FormData();
				Object.keys(ajaxData).forEach(function (name) {
					const value = ajaxData[name];
					if ("file" === name) {
						formData.append("file", value, value.name);
					} else {
						formData.append(name, value);
					}
				});

				jQuery.ajax({
					type: "POST",
					url: this.ajaxUrl,
					data: formData,
					processData: false,
					contentType: false,
					success: (response) => {
						if (
							response &&
							("undefined" !== typeof response.data ||
								response.success !== false)
						) {
							if (!options.createPage) {
								resolve({
									success: true,
									data: response.data || response,
									message: "Layout imported successfully (fallback)",
								});
                                return;
							}

							const imported_posts = response?.data?.imported_posts?.[0] ?? "";

							const createPageForm = new FormData();
							createPageForm.append(
								"action",
								"dll_create_page_with_imported_layout",
							);
							createPageForm.append("id", imported_posts);
							createPageForm.append("page_title", options.pageTitle);
							createPageForm.append("page_status", options.pageStatus);
							createPageForm.append("nonce", window.dllAjax.nonce);

							jQuery
								.ajax({
									type: "POST",
									url: this.ajaxUrl,
									data: createPageForm,
									processData: false,
									contentType: false,
								})
								.then((pageResponse) => {
									console.info(pageResponse);
									resolve(pageResponse);
								})
								.catch((pageError) => {
									reject(pageError);
								});
						} else {
							reject(new Error("Import failed - no data returned"));
						}
					},
					error: (xhr, status, error) => {
						console.error("Fallback AJAX error:", xhr, status, error);
						reject(new Error(`Network error: ${error}`));
					},
				});
			});
		} catch (error) {
			console.error("Fallback import error:", error);
			return {
				success: false,
				message: error.message || "Fallback import preparation failed",
			};
		}
	}

	/**
	 * Create page with layout
	 *
	 * @param {Object} layoutData The layout data to import
	 * @param {string} pageTitle The title for the new page
	 * @param {string} pageStatus The page status (draft, publish, etc.)
	 * @returns {Promise} Promise that resolves with page creation result
	 */
	async createPageWithLayout(layoutData, pageTitle, pageStatus = "draft") {
		return this.makeRequest("dll_create_page_with_layout", {
			layout_data: JSON.stringify(layoutData),
			page_title: pageTitle,
			page_status: pageStatus,
		});
	}

	/**
	 * Export layout
	 *
	 * @param {number} layoutId The layout ID to export
	 * @param {string} exportName Optional name for the export
	 * @returns {Promise} Promise that resolves with export result
	 */
	async exportLayout(layoutId, exportName = "") {
		return this.makeRequest("dll_export_layout", {
			layout_id: layoutId,
			export_name: exportName,
		});
	}

	/**
	 * Get available layouts for export
	 *
	 * @returns {Promise} Promise that resolves with layouts list
	 */
	async getAvailableLayouts() {
		return this.makeRequest("dll_get_layouts");
	}

	/**
	 * Verify import success by checking if layouts exist in Divi Library
	 *
	 * @param {Object} importData The data returned from import
	 * @returns {Promise} Promise that resolves with verification result
	 */
	async verifyImportSuccess(importData) {
		try {
			return {
				success: false,
				message: "No import data to verify",
			};
		} catch (error) {
			return {
				success: false,
				message: "Verification failed: " + error.message,
			};
		}
	}

	/**
	 * Download exported layout as JSON file
	 *
	 * @param {Object} exportData The export data from backend
	 * @param {string} filename The filename for download
	 */
	downloadLayoutFile(exportData, filename) {
		try {
			const jsonString = JSON.stringify(exportData, null, 2);
			const blob = new Blob([jsonString], { type: "application/json" });
			const url = URL.createObjectURL(blob);

			const link = document.createElement("a");
			link.href = url;
			link.download = `${filename}.json`;
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);

			URL.revokeObjectURL(url);
		} catch (error) {
			console.error("Download failed:", error);
			throw new Error("Failed to download layout file");
		}
	}

	/**
	 * Load layout data from JSON file
	 *
	 * @param {string} jsonUrl URL to the JSON file
	 * @returns {Promise} Promise that resolves with layout data
	 */
	async loadLayoutFromFile(jsonUrl) {
		try {
			const response = await fetch(jsonUrl);

			if (!response.ok) {
				throw new Error(`Failed to load layout file: ${response.status}`);
			}

			const layoutData = await response.json();

			// Optional: validate layout structure
			if (!this.validateLayoutData(layoutData)) {
				throw new Error("Invalid layout data structure");
			}

			return layoutData;
		} catch (error) {
			console.error("Failed to load layout from file:", error);
			throw error;
		}
	}

	/**
	 * Validate layout data structure
	 *
	 * @param {Object} layoutData The layout data to validate
	 * @returns {boolean} True if valid, false otherwise
	 */
	validateLayoutData(layoutData) {
		if (!layoutData || typeof layoutData !== "object") {
			return false;
		}
		// Check for required fields
		const requiredFields = ["context", "data"];
		for (const field of requiredFields) {
			if (!layoutData.hasOwnProperty(field)) {
				return false;
			}
		}

		// Validate context - accept both et_builder and et_builder_layouts
		const validContexts = ["et_builder", "et_builder_layouts"];
		if (!validContexts.includes(layoutData.context)) {
			return false;
		}

		// Validate data structure
		if (!layoutData.data || typeof layoutData.data !== "object") {
			return false;
		}

		return true;
	}

	/**
	 * Helper method to read file as text
	 * @param {File} file - File to read
	 * @returns {Promise<string>} - Promise resolving to file content
	 */
	readFileAsText(file) {
		return new Promise((resolve, reject) => {
			const reader = new FileReader();
			reader.onload = (e) => resolve(e.target.result);
			reader.onerror = () => reject(reader.error);
			reader.readAsText(file);
		});
	}

	/**
	 * Handle file upload for import
	 *
	 * @param {File} file The file to upload
	 * @returns {Promise} Promise that resolves with the file (ready for import)
	 */
	async handleFileUpload(file) {
		return new Promise((resolve, reject) => {
			if (!file) {
				reject(new Error("No file provided"));
				return;
			}

			if (file.type !== "application/json" && !file.name.endsWith(".json")) {
				reject(
					new Error(
						this.strings.invalidFile ||
							"Invalid file format. Please select a JSON file.",
					),
				);
				return;
			}

			// Basic validation - just check if it's valid JSON
			const reader = new FileReader();

			reader.onload = (event) => {
				try {
					JSON.parse(event.target.result);
					resolve(file); // Return the original file for import
				} catch (error) {
					reject(new Error("Failed to parse JSON file"));
				}
			};

			reader.onerror = () => {
				reject(new Error("Failed to read file"));
			};

			reader.readAsText(file);
		});
	}

	/**
	 * Get localized strings
	 *
	 * @param {string} key The string key
	 * @returns {string} Localized string
	 */
	getString(key) {
		return this.strings[key] || key;
	}
}

export default ApiService;
