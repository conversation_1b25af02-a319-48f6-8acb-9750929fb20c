{"name": "divi-layout-library", "version": "1.0.0", "description": "Divi Layout Library", "main": "index.js", "scripts": {"build": "wp-scripts build --mode=production", "check-engines": "wp-scripts check-engines", "check-licenses": "wp-scripts check-licenses", "format": "wp-scripts format", "lint:css": "wp-scripts lint-style", "lint:js": "wp-scripts lint-js", "lint:md:docs": "wp-scripts lint-md-docs", "lint:md:js": "wp-scripts lint-md-js", "lint:pkg-json": "wp-scripts lint-pkg-json", "packages-update": "wp-scripts packages-update", "start": "wp-scripts start --mode=development", "test:e2e": "wp-scripts test-e2e", "test:unit": "wp-scripts test-unit-js"}, "author": "<PERSON><PERSON><PERSON>", "dependencies": {}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-transform-class-properties": "^7.25.9", "@babel/plugin-transform-runtime": "^7.26.9", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@wordpress/babel-preset-default": "^8.18.0", "@wordpress/eslint-plugin": "^22.8.0", "@wordpress/prettier-config": "^4.22.0", "@wordpress/scripts": "^26.12.0", "@wordpress/stylelint-config": "^21.1.0", "babel-plugin-module-resolver": "^5.0.2", "copy-webpack-plugin": "^12.0.2", "mini-css-extract-plugin": "^2.9.2", "clean-webpack-plugin": "^4.0.0"}}