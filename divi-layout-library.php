<?php
/**
 * Plugin Name:     Divi Layout Library
 * Plugin URI:
 * Description:     A great plugin!
 * Version:         1.00
 * Author:          GutenSuite
 * Author URI:
 * License:         GPL-3.0+
 * License URI:     http://www.gnu.org/licenses/gpl-3.0.txt
 * Author URI:
 * Text Domain:     divi-layout-library
 * Domain Path:     /languages
 *
 * @package DiviLayoutLibrary
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit();
}

if ( file_exists( __DIR__ . '/vendor/autoload.php' ) ) {
	require_once __DIR__ . '/vendor/autoload.php';
}

if ( ! class_exists( 'DiviLayoutLibrary' ) ) {
	/**
	 * DiviLayoutLibrary class
	 *
	 * @since 1.0.0
	 */
	final class DiviLayoutLibrary {

		/**
		 * Constructor
		 *
		 * @since 1.0.0
		 */
		private function __construct() {
			$this->define_constants();
			register_activation_hook( __FILE__, array( $this, 'activate' ) );
			register_deactivation_hook( __FILE__, array( $this, 'deactivate' ) );
			add_action( 'init', array( $this, 'on_plugins_loaded' ) );
			add_action( 'divi_layout_library_loaded', array( $this, 'init_plugin' ) );
		}


		/**
		 * Initialize the plugin
		 *
		 * @return DiviLayoutLibrary
		 *
		 * @since 1.0.0
		 */
		public static function init() {
			static $instance = false;

			if ( ! $instance ) {
				$instance = new self();
			}

			return $instance;
		}

		/**
		 * Define constants
		 *
		 * @return void
		 *
		 * @since 1.0.0
		 */
		public function define_constants() {
			define( 'DIVI_LAYOUT_LIBARY_SLUG', 'divi-layout-library' );
			define( 'DIVI_LAYOUT_LIBARY_PLUGIN_ROOT_URI', plugins_url( '/', __FILE__ ) );
			define( 'DIVI_LAYOUT_LIBARY_ROOT_DIR_PATH', plugin_dir_path( __FILE__ ) );
			define( 'DIVI_LAYOUT_LIBARY_ROOT_DIR_URL', plugin_dir_url( __FILE__ ) );
			define( 'DIVI_LAYOUT_LIBARY_ASSETS_DIR_PATH', DIVI_LAYOUT_LIBARY_ROOT_DIR_PATH . 'assets/' );
			define( 'DIVI_LAYOUT_LIBARY_ASSETS_URI', DIVI_LAYOUT_LIBARY_PLUGIN_ROOT_URI . 'assets/' );
		}

		/**
		 * On plugins loaded
		 *
		 * @return void
		 */
		public function on_plugins_loaded() {
			do_action( 'divi_layout_library_loaded' );
		}

		/**
		 * Initialize the plugin
		 *
		 * @return void
		 */
		public function init_plugin() {
			if ( is_admin() ) {
				new DiviLayoutLibrary\Admin();
			}
		}

		/**
		 * Plugin activation hook
		 *
		 * @return void
		 */
		public function activate() {}

		/**
		 * Plugin deactivation hook
		 *
		 * @return void
		 */
		public function deactivate() {}
	}
}

if ( ! function_exists( 'divi_layout_library_start' ) ) {
	/**
	 * Initializes the main plugin
	 *
	 * @return \DiviLayoutLibrary
	 */
	function divi_layout_library_start() {
		return DiviLayoutLibrary::init();
	}
	divi_layout_library_start();
}
