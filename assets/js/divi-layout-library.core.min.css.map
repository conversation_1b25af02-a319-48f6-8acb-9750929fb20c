{"version": 3, "file": "divi-layout-library.core.min.css", "mappings": ";;;AA+DA;EACC;IACC;EA9DA;EAgED;IACC;EA9DA;AACF;AAkEA;EACC;EAEA,cAnEY;EAoEZ;AAjED;;AAqEA;EACC;AAlED;AAoEC;EAEC;EACA;EACA;EACA;AAnEF;AAsEC;EACC;EACA;EACA;EACA;EACA;EACA;AApEF;AAuEC;EACC;EACA;EACA;EACA,cAhGW;AA2Bb;AAwEC;EACC;EACA;EACA;AAtEF;AAyEC;EACC;EACA;AAvEF;AA0EC;EACC;AAxEF;;AA6EA;EACC;EACA;EACA;AA1ED;AA4EC;EACC,gBAvHM;EAwHN;EACA;EACA;EACA,WAxHU;EAyHV;AA1EF;AA4EE;EACC,yBAvIa;EAwIb,WAhIK;AAsDR;AA6EE;EACC;AA3EH;;AAiFA;EAnIC,yBAfe;EAgBf,WARO;EASP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAsDD;AApDC;EACC;EACA;EACA,WAxBM;AA8ER;AAnDC;EACC,6CACC;EAED;AAmDF;AAhDC;EACC;EACA;AAkDF;AAuDC;EAtIA,sBAPO;EAQP,cAXY;EAYZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAkFD;AAhFC;EACC;EACA;EACA,cA3BW;AA6Gb;AA/EC;EACC,0CACC;EAED;AA+EF;AA5EC;EACC;EACA;AA8EF;AA+BC;EACC,sBAlJM;EAmJN;AA7BF;AA+BC;EACC;EACA;AA7BF;AAgCC;EACC;EACA;AA9BF;;AAmCA;EACC;AAhCD;AAkCC;EAvHA;EACA;EACA;EACA;EAsHC;EACA;EACA;AA7BF;AA+BE;EACC;EACA;EACA;AA7BH;;AAmCA;EACC;EACA;AAhCD;AAkCC;EACC,cA7LY;EA8LZ;AAhCF;AAmCC;EACC;EACA,WA3LU;AA0JZ;;AAsCA;EACC;EACA;AAnCD;AAqCC;EACC;AAnCF;AAsCC;EACC;AApCF;AAuCC;EACC;EACA;EACA;EACA;EACA;EACA;EACA,cAzNW;AAoLb;AAuCE;EACC,cAjOa;AA4LhB;;AA2CA;EACC;EACA;EACA;AAxCD;AA0CC;EACC;AAxCF;AA2CC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAzCF;AA2CE;EACC,yBArPU;AA4Mb;AA4CE;EACC;EACA,WA3PK;AAiNR;AA8CC;EACC;AA5CF;AA+CC;EACC,yBAlQY;EAmQZ,WAlQU;EAmQV;EACA;EACA;EACA;AA7CF;AA+CE;EACC;EACA,WA7QK;AAgOR;;AAoDC;EACC;EACA;EACA;AAjDF;AAoDC;EACC;EACA;EACA;AAlDF;AAqDC;EACC;EACA;EACA,WAhSU;EAiSV;AAnDF;;AAwDA;EACC,gBA1SO;EA2SP;EACA;EACA;EACA;EArQA;AAiND;AAuDC;EACC;AArDF;AAyDE;EACC;EACA;EACA;AAvDH;AA0DE;EACC;AAxDH;AA4DC;EACC;EACA;AA1DF;AA4DE;EACC;EACA;AA1DH;AA6DE;EACC;EACA;EACA;AA3DH;AA8DE;EACC;EACA;EACA;EACA;EACA;AA5DH;AA+DE;EACC;AA7DH;AAiEC;EACC;EACA;EACA;KAAA;EACA;AA/DF;AAiEE;EACC;AA/DH;AAmEC;EAEC;EACA;EACA;EACA;EACA,yBA7WW;EA8WX,WA5WU;EA6WV;EACA;AAlEF;AAoEE;EACC;AAlEH;AAsEC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AApEF;AAsEE;EACC;AApEH;AAwEC;EACC;EACA;AAtEF;AAwEE;EACC,gBA/YK;AAyUR;AAwEG;EACC;AAtEJ;AA2EC;EACC;EACA;EACA;EACA,cA9ZW;AAqVb;AA4EC;EACC;EACA,cAxac;EAyad;EACA;EACA;EACA;AA1EF;AA6EC;EACC;EACA,WAtaU;EAuaV;EACA;AA3EF;;AAgFA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AA7ED;;AAgFA;EACC,gBA/bO;EAgcP;EACA;EACA;EACA;EACA;EACA;EACA;EA7ZA;AAiVD;AA+EC;EACC;EACA;EACA;EACA;EACA;EACA,mBA9cW;AAiYb;AAgFC;EACC;EACA;EACA;EACA;EACA;EACA;AA9EF;AAgFE;EACC,cAnea;AAqZhB;AAkFC;EACC;EACA;EACA;EACA;EACA;EACA,WAleU;AAkZZ;AAkFE;EACC,yBAteW;AAsZd;AAmFE;EACC;AAjFH;AAqFC;EACC;EACA;EACA;AAnFF;AAsFC;EACC;EACA;EACA;EACA;EACA;EACA,mBA3fW;AAuab;;AAyFA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAtFD;;AAyFA;EACC,gBAhhBO;EAihBP;EACA;EACA;EACA;EACA;AAtFD;AAwFC;EACC,gBAxhBM;EAyhBN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAtFF;AAyFC;EACC;EACA;AAvFF;AA0FC;EACC;EACA;EACA;EACA;EACA;AAxFF;AA2FC;EACC;EACA;EACA;EACA;EACA;AAzFF;AA4FC;EACC;EACA;EACA;EACA,cA/jBW;EAgkBX;EACA;EACA;AA1FF;AA6FC;EACC;AA3FF;AA8FC;EACC;EACA;EACA;EACA;EACA,gBA3kBM;EA4kBN;EACA;EACA;EACA;EACA;EACA;EACA;AA5FF;AA8FE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AA5FH;AA+FE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AA7FH;AAgGE;EACC;AA9FH;AAgGG;EACC;AA9FJ;AAkGE;EACC;EACA;EACA;AAhGH;AAoGC;EACC;EACA;EACA;EACA;EACA;EACA,WAhoBU;EAioBV;EACA;EACA;AAlGF;AAoGE;EACC,yBAvoBW;AAqiBd;AAqGE;EACC;EACA;AAnGH;AAuGC;EACC;EACA;EACA;AArGF;AAwGC;EACC;EACA;EACA;EACA,gBA5pBM;AAsjBR;AAyGC;EACC;EACA;EACA;EACA;EACA;EACA;EACA,mBArqBW;EAsqBX,WApqBU;EAqqBV;EACA;AAvGF;AAyGE;EACC;EACA;AAvGH;AA0GE;EACC;EACA;EACA;AAxGH;AA2GE;EACC;EACA;EACA;AAzGH;;AA+GA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;AA5GD;;AAgHA;EACC;AA7GD;;AAgHA;EACC;EACA;EACA;EACA,cArtBY;AAwmBb;;AAgHA;;EAEC;EACA;EACA;EACA;EACA;EACA;EACA;AA7GD;AA+GC;;EACC;EACA,qBAzuBc;EA0uBd;AA5GF;;AAgHA;EACC;EACA;EACA,WAtuBW;AAynBZ;;AAiHA;EACC;EACA;EACA;AA9GD;AAgHC;EACC;AA9GF;AAiHC;EACC;EACA;EACA;AA/GF;;AAqHC;EACC;AAlHF;AAoHE;EACC;EACA;AAlHH;AAsHC;EACC,mBAxwBW;EAywBX;EACA;AApHF;;AAwHA;EACC;EACA;AArHD;AAuHC;EACC;EACA;EACA,cAzxBW;AAoqBb;;AAyHA;EACC;AAtHD;AAwHC;EACC;EACA;EACA,mBA9xBY;EA+xBZ;EACA;AAtHF;AAyHC;EACC;EACA;EACA;EACA;AAvHF;AA0HC;EACC;EACA;EACA;EACA,cAnzBW;AA2rBb;;AA4HA;EACC;EACA;EACA;AAzHD;AA2HC;EACC;EACA,cAj0Bc;EAk0Bd;AAzHF;AA2HE;EACC;AAzHH;AA6HC;EACC;EACA;EACA,cAz0BW;AA8sBb;AA8HC;EACC;EACA,WAx0BU;AA4sBZ;AA+HC;EACC;EACA;EACA;AA7HF;;AAiIA;EACC;EACA;AA9HD;AAgIC;EACC;EACA;AA9HF;AAiIC;EACC;EACA,cAr2BY;EAs2BZ;AA/HF;AAkIC;EACC;EACA,WAn2BU;AAmuBZ;;AAqIA;EACC;AAlID;;AAqIA;EACC;AAlID;AAoIC;EACC;EACA;EACA,cAx3BW;AAsvBb;;AAuIC;EACC;EACA;EACA,WA13BU;AAsvBZ;AAuIC;EACC;EACA;EACA;EACA;AArIF;;AAyIA;EACC;EACA;EACA;EACA;EACA;EACA;AAtID;AAwIC;EACC;AAtIF;AAyIC;EACC,yBAp5BW;AA6wBb;AA0IC;EACC;EACA,qBAl6Bc;AA0xBhB;AA2IC;EACC;AAzIF;AA4IC;EACC;EACA;EACA;EACA,cAx6BW;AA8xBb;AA6IC;EACC;EACA;EACA;EACA,WAz6BU;AA8xBZ;AA8IC;EAEC;AA7IF;AAgJC;EACC;AA9IF;AAiJC;EACC,WAt7BU;EAu7BV;EACA;EACA;EACA;AA/IF;AAiJE;EACC,yBA97BW;EA+7BX,cAz8Ba;AA0zBhB;;AAoJA;EACC;EACA;AAjJD;AAmJC;EACC;EACA,cAl9Bc;EAm9Bd;AAjJF;AAmJE;EACC;AAjJH;AAqJC;EACC,cA39Bc;EA49Bd;AAnJF;;AAuJA;EACC;EACA,cAj+Ba;EAk+Bb;EACA;EACA;EACA;EACA;EACA;AApJD;AAsJC;EACC;AApJF;;AAyJA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;AAtJD;AAwJC;EACC;EACA;EACA;EACA;EACA;AAtJF;;AA0JA;EACC;IACC;IACA;EAvJA;EAyJD;IACC;IACA;EAvJA;AACF;AA2JA;EACC;IACC;EAzJA;EA2JA;IACC;IACA;IACA;EAzJD;EA4JA;IACC;IACA;EA1JD;EA6JA;IACC;IACA;EA3JD;EA+JD;IACC;EA7JA;EAiKA;IACC;IACA;EA/JD;EAoKA;IACC;EAlKD;EAoKC;IACC;EAlKF;EAqKC;IACC;IACA;IACA;EAnKF;EAsKC;IACC;EApKF;EAyKD;IACC;IACA;EAvKA;EAyKA;IACC;EAvKD;EA0KA;IACC;IACA;IACA;EAxKD;EA0KC;IACC;IACA;EAxKF;EA6KD;IACC;IACA;EA3KA;EA6KA;IACC;IACA;EA3KD;AACF,C", "sources": ["webpack://divi-layout-library/./react_app/index.scss"], "sourcesContent": ["// Divi Layout Library Styles\n// Following BEM naming convention and WordPress admin styling\n\n// Variables\n$primary-color: #0073aa;\n$secondary-color: #00a0d2;\n$success-color: #46b450;\n$error-color: #dc3232;\n$warning-color: #ffb900;\n$text-color: #23282d;\n$border-color: #ddd;\n$background-color: #f1f1f1;\n$white: #fff;\n$gray-light: #f9f9f9;\n$gray-medium: #e5e5e5;\n$gray-dark: #666;\n\n// Mixins\n@mixin button-style($bg-color, $text-color: $white) {\n\tbackground-color: $bg-color;\n\tcolor: $text-color;\n\tborder: 1px solid darken($bg-color, 10%);\n\tpadding: 8px 16px;\n\tborder-radius: 3px;\n\tcursor: pointer;\n\ttext-decoration: none;\n\tdisplay: inline-flex;\n\talign-items: center;\n\tgap: 5px;\n\tfont-size: 13px;\n\tline-height: 1.4;\n\ttransition: all 0.2s ease;\n\n\t&:hover {\n\t\tbackground-color: darken($bg-color, 5%);\n\t\tborder-color: darken($bg-color, 15%);\n\t\tcolor: $text-color;\n\t}\n\n\t&:focus {\n\t\tbox-shadow:\n\t\t\t0 0 0 1px $white,\n\t\t\t0 0 0 3px $bg-color;\n\t\toutline: none;\n\t}\n\n\t&:disabled {\n\t\topacity: 0.6;\n\t\tcursor: not-allowed;\n\t}\n}\n\n@mixin card-shadow {\n\tbox-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n@mixin loading-spinner {\n\tborder: 2px solid $gray-medium;\n\tborder-top: 2px solid $primary-color;\n\tborder-radius: 50%;\n\tanimation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n\t0% {\n\t\ttransform: rotate(0deg);\n\t}\n\t100% {\n\t\ttransform: rotate(360deg);\n\t}\n}\n\n// Main App Container\n.dll-app {\n\tfont-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto,\n\t\tOxygen-Sans, Ubuntu, Cantarell, \"Helvetica Neue\", sans-serif;\n\tcolor: $text-color;\n\tline-height: 1.4;\n}\n\n// Dashboard\n.dll-dashboard {\n\tpadding: 20px;\n\n\t&--loading,\n\t&--error {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmin-height: 400px;\n\t}\n\n\t&__header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 30px;\n\t\tpadding-bottom: 20px;\n\t\tborder-bottom: 1px solid $border-color;\n\t}\n\n\t&__title {\n\t\tmargin: 0;\n\t\tfont-size: 24px;\n\t\tfont-weight: 600;\n\t\tcolor: $text-color;\n\t}\n\n\t&__toolbar {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 15px;\n\t}\n\n\t&__content {\n\t\tdisplay: flex;\n\t\tgap: 30px;\n\t}\n\n\t&__main {\n\t\tflex: 1;\n\t}\n}\n\n// View Toggle\n.dll-view-toggle {\n\tdisplay: flex;\n\tborder-radius: 3px;\n\toverflow: hidden;\n\n\t&__button {\n\t\tbackground: $white;\n\t\tborder: none;\n\t\tpadding: 8px 12px;\n\t\tcursor: pointer;\n\t\tcolor: $gray-dark;\n\t\ttransition: all 0.2s ease;\n\n\t\t&--active {\n\t\t\tbackground-color: $primary-color;\n\t\t\tcolor: $white;\n\t\t}\n\n\t\t.dashicons {\n\t\t\tfont-size: 16px;\n\t\t}\n\t}\n}\n\n// Buttons\n.dll-button {\n\t@include button-style($primary-color);\n\n\t&--secondary {\n\t\t@include button-style($white, $text-color);\n\t}\n\n\t&.button-secondary {\n\t\tbackground-color: $white;\n\t\tborder-color: transparent;\n\t}\n\t&--small {\n\t\tpadding: 6px 12px;\n\t\tfont-size: 12px;\n\t}\n\n\t.dashicons {\n\t\tfont-size: 14px;\n\t\tline-height: 1.4rem;\n\t}\n}\n\n// Loading\n.dll-loading {\n\ttext-align: center;\n\n\t&__spinner {\n\t\t@include loading-spinner;\n\t\twidth: 40px;\n\t\theight: 40px;\n\t\tmargin: 0 auto 15px;\n\n\t\t&--small {\n\t\t\twidth: 16px;\n\t\t\theight: 16px;\n\t\t\tborder-width: 1px;\n\t\t}\n\t}\n}\n\n// Error\n.dll-error {\n\ttext-align: center;\n\tpadding: 40px 20px;\n\n\th3 {\n\t\tcolor: $error-color;\n\t\tmargin-bottom: 10px;\n\t}\n\n\tp {\n\t\tmargin-bottom: 20px;\n\t\tcolor: $gray-dark;\n\t}\n}\n\n// Sidebar\n.dll-sidebar {\n\twidth: 250px;\n\tflex-shrink: 0;\n\n\t&__header {\n\t\tmargin-bottom: 20px;\n\t}\n\n\t&__search {\n\t\tmargin-bottom: 10px;\n\t}\n\n\t&__title {\n\t\tfont-size: 16px;\n\t\tfont-weight: 600;\n\t\tmargin: 0;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 8px;\n\t\tcolor: $text-color;\n\n\t\t.dashicons {\n\t\t\tcolor: $primary-color;\n\t\t}\n\t}\n}\n\n// Category List\n.dll-category-list {\n\tlist-style: none;\n\tmargin: 0;\n\tpadding: 0;\n\n\t&__item {\n\t\tmargin-bottom: 2px;\n\t}\n\n\t&__button {\n\t\twidth: 100%;\n\t\tbackground: none;\n\t\tborder: none;\n\t\tpadding: 10px 15px;\n\t\ttext-align: left;\n\t\tcursor: pointer;\n\t\tborder-radius: 3px;\n\t\ttransition: all 0.2s ease;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\n\t\t&:hover {\n\t\t\tbackground-color: $gray-light;\n\t\t}\n\n\t\t&--active {\n\t\t\tbackground-color: $primary-color !important;\n\t\t\tcolor: $white;\n\t\t}\n\t}\n\n\t&__name {\n\t\tfont-weight: 500;\n\t}\n\n\t&__count {\n\t\tbackground-color: $gray-medium;\n\t\tcolor: $gray-dark;\n\t\tpadding: 2px 6px;\n\t\tborder-radius: 10px;\n\t\tfont-size: 11px;\n\t\tfont-weight: 600;\n\n\t\t.dll-category-list__button--active & {\n\t\t\tbackground-color: rgba(255, 255, 255, 0.2);\n\t\t\tcolor: $white;\n\t\t}\n\t}\n}\n\n// Layouts Grid/List\n.dll-layouts {\n\t&--grid {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n\t\tgap: 20px;\n\t}\n\n\t&--list {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 15px;\n\t}\n\n\t&__empty {\n\t\ttext-align: center;\n\t\tpadding: 60px 20px;\n\t\tcolor: $gray-dark;\n\t\tgrid-column: 1 / -1;\n\t}\n}\n\n// Layout Card\n.dll-layout-card {\n\tbackground: $white;\n\tborder: 1px solid $border-color;\n\tborder-radius: 6px;\n\toverflow: hidden;\n\ttransition: all 0.3s ease;\n\t@include card-shadow;\n\n\t&:hover {\n\t\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n\t}\n\n\t&--grid {\n\t\t.dll-layout-card__image-container {\n\t\t\theight: 200px;\n\t\t\tposition: relative;\n\t\t\toverflow: hidden;\n\t\t}\n\n\t\t.dll-layout-card__content {\n\t\t\tpadding: 15px;\n\t\t}\n\t}\n\n\t&--list {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\n\t\t.dll-layout-card__image-wrapper {\n\t\t\twidth: 150px;\n\t\t\tflex-shrink: 0;\n\t\t}\n\n\t\t.dll-layout-card__image-container {\n\t\t\theight: 100px;\n\t\t\tposition: relative;\n\t\t\toverflow: hidden;\n\t\t}\n\n\t\t.dll-layout-card__content-wrapper {\n\t\t\tflex: 1;\n\t\t\tpadding: 15px;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t.dll-layout-card__actions-list {\n\t\t\tmargin-left: 20px;\n\t\t}\n\t}\n\n\t&__image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t\ttransition: transform 0.3s ease;\n\n\t\t&--scrolling {\n\t\t\ttransform: translateY(-20%);\n\t\t}\n\t}\n\n\t&__image-placeholder,\n\t&__image-error {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\theight: 100%;\n\t\tbackground-color: $gray-light;\n\t\tcolor: $gray-dark;\n\t\tflex-direction: column;\n\t\tgap: 10px;\n\n\t\t.dashicons {\n\t\t\tfont-size: 24px;\n\t\t}\n\t}\n\n\t&__overlay {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: rgba(0, 0, 0, 0.7);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\topacity: 0;\n\t\ttransition: opacity 0.3s ease;\n\n\t\t.dll-layout-card:hover & {\n\t\t\topacity: 1;\n\t\t}\n\t}\n\n\t&__actions {\n\t\tdisplay: flex;\n\t\tgap: 10px;\n\n\t\t> button.is-secondary {\n\t\t\tbackground: $white;\n\n\t\t\t&:hover {\n\t\t\t\tbackground: $white !important;\n\t\t\t}\n\t\t}\n\t}\n\n\t&__title {\n\t\tfont-size: 16px;\n\t\tfont-weight: 600;\n\t\tmargin: 0 0 5px 0;\n\t\tcolor: $text-color;\n\t}\n\n\t&__category {\n\t\tfont-size: 12px;\n\t\tcolor: $primary-color;\n\t\tfont-weight: 500;\n\t\tmargin: 0 0 8px 0;\n\t\ttext-transform: uppercase;\n\t\tletter-spacing: 0.5px;\n\t}\n\n\t&__description {\n\t\tfont-size: 13px;\n\t\tcolor: $gray-dark;\n\t\tmargin: 0;\n\t\tline-height: 1.4;\n\t}\n}\n\n// Modal\n.dll-modal-overlay {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: rgba(0, 0, 0, 0.5);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tz-index: 100000;\n\tpadding: 20px;\n}\n\n.dll-modal {\n\tbackground: $white;\n\tborder-radius: 6px;\n\tmax-width: 600px;\n\twidth: 100%;\n\tmax-height: 90vh;\n\toverflow: hidden;\n\tdisplay: flex;\n\tflex-direction: column;\n\t@include card-shadow;\n\n\t&__header {\n\t\tpadding: 20px;\n\t\tborder-bottom: 1px solid $border-color;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tbackground: $gray-light;\n\t}\n\n\t&__title {\n\t\tmargin: 0;\n\t\tfont-size: 18px;\n\t\tfont-weight: 600;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 8px;\n\n\t\t.dashicons {\n\t\t\tcolor: $primary-color;\n\t\t}\n\t}\n\n\t&__close {\n\t\tbackground: none;\n\t\tborder: none;\n\t\tpadding: 5px;\n\t\tcursor: pointer;\n\t\tborder-radius: 3px;\n\t\tcolor: $gray-dark;\n\n\t\t&:hover {\n\t\t\tbackground-color: $gray-medium;\n\t\t}\n\n\t\t.dashicons {\n\t\t\tfont-size: 18px;\n\t\t}\n\t}\n\n\t&__content {\n\t\tpadding: 20px;\n\t\toverflow-y: auto;\n\t\tflex: 1;\n\t}\n\n\t&__footer {\n\t\tpadding: 20px;\n\t\tborder-top: 1px solid $border-color;\n\t\tdisplay: flex;\n\t\tjustify-content: flex-end;\n\t\tgap: 10px;\n\t\tbackground: $gray-light;\n\t}\n}\n\n// Preview Modal\n.dll-preview-modal-overlay {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: rgba(0, 0, 0, 0.8);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tz-index: 100001; // Higher than regular modals\n\tpadding: 0;\n}\n\n.dll-preview-modal {\n\tbackground: $white;\n\twidth: 90vw;\n\theight: 90vh;\n\tdisplay: flex;\n\tflex-direction: column;\n\toverflow: hidden;\n\n\t&__header {\n\t\tbackground: $white;\n\t\tborder-bottom: 1px solid $border-color;\n\t\tpadding: 15px 20px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tflex-shrink: 0;\n\t\tbox-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n\t\tz-index: 1;\n\t}\n\n\t&__header-left {\n\t\tflex: 1;\n\t\tmin-width: 0;\n\t}\n\n\t&__header-center {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 15px;\n\t\tflex: 2;\n\t\tjustify-content: center;\n\t}\n\n\t&__header-right {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 10px;\n\t\tflex: 1;\n\t\tjustify-content: flex-end;\n\t}\n\n\t&__title {\n\t\tmargin: 0;\n\t\tfont-size: 18px;\n\t\tfont-weight: 600;\n\t\tcolor: $text-color;\n\t\twhite-space: nowrap;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t}\n\n\t&__create-page {\n\t\tposition: relative;\n\t}\n\n\t&__create-page-form {\n\t\tposition: absolute;\n\t\ttop: 100%;\n\t\tleft: 50%;\n\t\ttransform: translateX(-50%);\n\t\tbackground: $white;\n\t\tborder: 1px solid $border-color;\n\t\tborder-radius: 6px;\n\t\tpadding: 20px;\n\t\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n\t\tz-index: 1000;\n\t\tmin-width: 300px;\n\t\tmargin-top: 10px;\n\n\t\t&::before {\n\t\t\tcontent: \"\";\n\t\t\tposition: absolute;\n\t\t\ttop: -8px;\n\t\t\tleft: 50%;\n\t\t\ttransform: translateX(-50%);\n\t\t\twidth: 0;\n\t\t\theight: 0;\n\t\t\tborder-left: 8px solid transparent;\n\t\t\tborder-right: 8px solid transparent;\n\t\t\tborder-bottom: 8px solid $white;\n\t\t}\n\n\t\t&::after {\n\t\t\tcontent: \"\";\n\t\t\tposition: absolute;\n\t\t\ttop: -9px;\n\t\t\tleft: 50%;\n\t\t\ttransform: translateX(-50%);\n\t\t\twidth: 0;\n\t\t\theight: 0;\n\t\t\tborder-left: 9px solid transparent;\n\t\t\tborder-right: 9px solid transparent;\n\t\t\tborder-bottom: 9px solid $border-color;\n\t\t}\n\n\t\t.dll-form-group {\n\t\t\tmargin-bottom: 15px;\n\n\t\t\t&:last-of-type {\n\t\t\t\tmargin-bottom: 20px;\n\t\t\t}\n\t\t}\n\n\t\t.dll-form-actions {\n\t\t\tdisplay: flex;\n\t\t\tgap: 10px;\n\t\t\tjustify-content: flex-end;\n\t\t}\n\t}\n\n\t&__close {\n\t\tbackground: none;\n\t\tborder: none;\n\t\tpadding: 8px;\n\t\tcursor: pointer;\n\t\tborder-radius: 3px;\n\t\tcolor: $gray-dark;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\n\t\t&:hover {\n\t\t\tbackground-color: $gray-medium;\n\t\t}\n\n\t\t&:disabled {\n\t\t\topacity: 0.6;\n\t\t\tcursor: not-allowed;\n\t\t}\n\t}\n\n\t&__content {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\toverflow: hidden;\n\t}\n\n\t&__iframe {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tborder: none;\n\t\tbackground: $white;\n\t}\n\n\t&__no-preview {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: $gray-light;\n\t\tcolor: $gray-dark;\n\t\ttext-align: center;\n\t\tpadding: 40px;\n\n\t\t&-icon {\n\t\t\tmargin-bottom: 20px;\n\t\t\topacity: 0.5;\n\t\t}\n\n\t\th3 {\n\t\t\tmargin: 0 0 10px 0;\n\t\t\tfont-size: 24px;\n\t\t\tfont-weight: 600;\n\t\t}\n\n\t\tp {\n\t\t\tmargin: 0;\n\t\t\tfont-size: 16px;\n\t\t\tmax-width: 400px;\n\t\t}\n\t}\n}\n\n// Error message styling\n.dll-error-message {\n\tbackground: lighten($error-color, 40%);\n\tborder: 1px solid lighten($error-color, 20%);\n\tcolor: darken($error-color, 10%);\n\tpadding: 8px 12px;\n\tborder-radius: 3px;\n\tfont-size: 13px;\n\tmargin-bottom: 15px;\n}\n\n// Forms\n.dll-form-group {\n\tmargin-bottom: 20px;\n}\n\n.dll-form-label {\n\tdisplay: block;\n\tmargin-bottom: 5px;\n\tfont-weight: 500;\n\tcolor: $text-color;\n}\n\n.dll-form-input,\n.dll-form-select {\n\twidth: 100%;\n\tmax-width: 100% !important;\n\tpadding: 8px 12px;\n\tborder: 1px solid $border-color;\n\tborder-radius: 3px;\n\tfont-size: 14px;\n\ttransition: border-color 0.2s ease;\n\n\t&:focus {\n\t\toutline: none;\n\t\tborder-color: $primary-color;\n\t\tbox-shadow: 0 0 0 1px $primary-color;\n\t}\n}\n\n.dll-form-help {\n\tmargin-top: 5px;\n\tfont-size: 12px;\n\tcolor: $gray-dark;\n}\n\n// Radio Options\n.dll-radio-option {\n\tdisplay: block;\n\tmargin-bottom: 15px;\n\tcursor: pointer;\n\n\tinput[type=\"radio\"] {\n\t\tmargin-right: 10px;\n\t}\n\n\t&__label {\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\t\tgap: 10px;\n\t}\n}\n\n// Import Modal Specific\n.dll-import-modal {\n\t.dll-import-options {\n\t\tmargin-bottom: 25px;\n\n\t\th3 {\n\t\t\tmargin-bottom: 15px;\n\t\t\tfont-size: 16px;\n\t\t}\n\t}\n\n\t.dll-page-options {\n\t\tbackground: $gray-light;\n\t\tborder-radius: 3px;\n\t\tmargin-top: 15px;\n\t}\n}\n\n.dll-import-progress {\n\ttext-align: center;\n\tpadding: 40px 20px;\n\n\t&__text {\n\t\tmargin-bottom: 20px;\n\t\tfont-size: 16px;\n\t\tcolor: $text-color;\n\t}\n}\n\n.dll-progress {\n\tmargin-bottom: 15px;\n\n\t&__bar {\n\t\twidth: 100%;\n\t\theight: 8px;\n\t\tbackground: $gray-medium;\n\t\tborder-radius: 4px;\n\t\toverflow: hidden;\n\t}\n\n\t&__fill {\n\t\theight: 100%;\n\t\tbackground: linear-gradient(90deg, $primary-color, $secondary-color);\n\t\ttransition: width 0.3s ease;\n\t\tborder-radius: 4px;\n\t}\n\n\t&__text {\n\t\tmargin-top: 10px;\n\t\tfont-size: 14px;\n\t\tfont-weight: 500;\n\t\tcolor: $text-color;\n\t}\n}\n\n.dll-import-success {\n\ttext-align: center;\n\tpadding: 40px 20px;\n\tposition: relative;\n\n\t&__icon {\n\t\tfont-size: 48px;\n\t\tcolor: $success-color;\n\t\tmargin-bottom: 15px;\n\n\t\t.dashicons {\n\t\t\tfont-size: 48px;\n\t\t}\n\t}\n\n\t&__title {\n\t\tfont-size: 20px;\n\t\tmargin-bottom: 10px;\n\t\tcolor: $text-color;\n\t}\n\n\t&__message {\n\t\tmargin-bottom: 25px;\n\t\tcolor: $gray-dark;\n\t}\n\n\t&__actions {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tgap: 10px;\n\t}\n}\n\n.dll-import-error {\n\ttext-align: center;\n\tpadding: 40px 20px;\n\n\t&__icon {\n\t\tfont-size: 48px;\n\t\tmargin-bottom: 15px;\n\t}\n\n\t&__title {\n\t\tfont-size: 18px;\n\t\tcolor: $error-color;\n\t\tmargin-bottom: 10px;\n\t}\n\n\t&__message {\n\t\tmargin-bottom: 25px;\n\t\tcolor: $gray-dark;\n\t}\n}\n\n// Export Modal Specific\n.dll-export-modal {\n\tmax-width: 700px;\n}\n\n.dll-export-step {\n\tmargin-bottom: 30px;\n\n\th3 {\n\t\tmargin-bottom: 15px;\n\t\tfont-size: 16px;\n\t\tcolor: $text-color;\n\t}\n}\n\n.dll-layout-list {\n\t&__empty {\n\t\ttext-align: center;\n\t\tpadding: 40px 20px;\n\t\tcolor: $gray-dark;\n\t}\n\n\t&__items {\n\t\tmax-height: 300px;\n\t\toverflow-y: auto;\n\t\tborder: 1px solid $border-color;\n\t\tborder-radius: 3px;\n\t}\n}\n\n.dll-layout-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 15px;\n\tborder-bottom: 1px solid $border-color;\n\tcursor: pointer;\n\ttransition: background-color 0.2s ease;\n\n\t&:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\t&:hover {\n\t\tbackground-color: $gray-light;\n\t}\n\n\t&--selected {\n\t\tbackground-color: lighten($primary-color, 45%);\n\t\tborder-color: $primary-color;\n\t}\n\n\t&__content {\n\t\tflex: 1;\n\t}\n\n\t&__title {\n\t\tmargin: 0 0 5px 0;\n\t\tfont-size: 14px;\n\t\tfont-weight: 600;\n\t\tcolor: $text-color;\n\t}\n\n\t&__meta {\n\t\tdisplay: flex;\n\t\tgap: 15px;\n\t\tfont-size: 12px;\n\t\tcolor: $gray-dark;\n\t}\n\n\t&__type,\n\t&__status {\n\t\ttext-transform: capitalize;\n\t}\n\n\t&__actions {\n\t\tmargin-left: 15px;\n\t}\n\n\t&__edit {\n\t\tcolor: $gray-dark;\n\t\ttext-decoration: none;\n\t\tpadding: 5px;\n\t\tborder-radius: 3px;\n\t\ttransition: background-color 0.2s ease;\n\n\t\t&:hover {\n\t\t\tbackground-color: $gray-medium;\n\t\t\tcolor: $primary-color;\n\t\t}\n\t}\n}\n\n.dll-export-success {\n\ttext-align: center;\n\tpadding: 40px 20px;\n\n\t&__icon {\n\t\tfont-size: 48px;\n\t\tcolor: $success-color;\n\t\tmargin-bottom: 15px;\n\n\t\t.dashicons {\n\t\t\tfont-size: 48px;\n\t\t}\n\t}\n\n\th3 {\n\t\tcolor: $success-color;\n\t\tmargin-bottom: 10px;\n\t}\n}\n\n.dll-export-error-inline {\n\tbackground-color: lighten($error-color, 45%);\n\tcolor: $error-color;\n\tpadding: 10px 15px;\n\tborder-radius: 3px;\n\tmargin-top: 15px;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8px;\n\n\t.dashicons {\n\t\tflex-shrink: 0;\n\t}\n}\n\n// Confetti Animation\n.dll-confetti {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tpointer-events: none;\n\toverflow: hidden;\n\n\t&__piece {\n\t\tposition: absolute;\n\t\twidth: 8px;\n\t\theight: 8px;\n\t\ttop: -10px;\n\t\tanimation: confetti-fall 3s linear infinite;\n\t}\n}\n\n@keyframes confetti-fall {\n\t0% {\n\t\ttransform: translateY(-100vh) rotate(0deg);\n\t\topacity: 1;\n\t}\n\t100% {\n\t\ttransform: translateY(100vh) rotate(360deg);\n\t\topacity: 0;\n\t}\n}\n\n// Responsive Design\n@media (max-width: 768px) {\n\t.dll-dashboard {\n\t\tpadding: 15px;\n\n\t\t&__header {\n\t\t\tflex-direction: column;\n\t\t\talign-items: flex-start;\n\t\t\tgap: 15px;\n\t\t}\n\n\t\t&__toolbar {\n\t\t\twidth: 100%;\n\t\t\tjustify-content: space-between;\n\t\t}\n\n\t\t&__content {\n\t\t\tflex-direction: column;\n\t\t\tgap: 20px;\n\t\t}\n\t}\n\n\t.dll-sidebar {\n\t\twidth: 100%;\n\t}\n\n\t.dll-layouts {\n\t\t&--grid {\n\t\t\tgrid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n\t\t\tgap: 15px;\n\t\t}\n\t}\n\n\t.dll-layout-card {\n\t\t&--list {\n\t\t\tflex-direction: column;\n\n\t\t\t.dll-layout-card__image-wrapper {\n\t\t\t\twidth: 100%;\n\t\t\t}\n\n\t\t\t.dll-layout-card__content {\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: flex-start;\n\t\t\t\tgap: 15px;\n\t\t\t}\n\n\t\t\t.dll-layout-card__actions-list {\n\t\t\t\tmargin-left: 0;\n\t\t\t}\n\t\t}\n\t}\n\n\t.dll-modal {\n\t\tmargin: 10px;\n\t\tmax-height: calc(100vh - 20px);\n\n\t\t&__content {\n\t\t\tpadding: 15px;\n\t\t}\n\n\t\t&__footer {\n\t\t\tpadding: 15px;\n\t\t\tflex-direction: column;\n\t\t\tgap: 10px;\n\n\t\t\t.dll-button {\n\t\t\t\twidth: 100%;\n\t\t\t\tjustify-content: center;\n\t\t\t}\n\t\t}\n\t}\n\n\t.dll-import-success__actions {\n\t\tflex-direction: column;\n\t\tgap: 10px;\n\n\t\t.dll-button {\n\t\t\twidth: 100%;\n\t\t\tjustify-content: center;\n\t\t}\n\t}\n}\n"], "names": [], "sourceRoot": ""}