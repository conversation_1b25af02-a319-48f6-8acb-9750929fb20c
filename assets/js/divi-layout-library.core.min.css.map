{"version": 3, "file": "divi-layout-library.core.min.css", "mappings": ";;;AA+DA;EACC;IACC;EA9DA;EAgED;IACC;EA9DA;AACF;AAkEA;EACC;EAEA,cAnEY;EAoEZ;AAjED;;AAqEA;EACC;AAlED;AAoEC;EAEC;EACA;EACA;EACA;AAnEF;AAsEC;EACC;EACA;EACA;EACA;EACA;EACA;AApEF;AAuEC;EACC;EACA;EACA;EACA,cAhGW;AA2Bb;AAwEC;EACC;EACA;EACA;AAtEF;AAyEC;EACC;EACA;AAvEF;AA0EC;EACC;AAxEF;;AA6EA;EACC;EACA;EACA;AA1ED;AA4EC;EACC,gBAvHM;EAwHN;EACA;EACA;EACA,WAxHU;EAyHV;AA1EF;AA4EE;EACC,yBAvIa;EAwIb,WAhIK;AAsDR;AA6EE;EACC;AA3EH;;AAiFA;EAnIC,yBAfe;EAgBf,WARO;EASP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAsDD;AApDC;EACC;EACA;EACA,WAxBM;AA8ER;AAnDC;EACC,6CACC;EAED;AAmDF;AAhDC;EACC;EACA;AAkDF;AAuDC;EAtIA,sBAPO;EAQP,cAXY;EAYZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAkFD;AAhFC;EACC;EACA;EACA,cA3BW;AA6Gb;AA/EC;EACC,0CACC;EAED;AA+EF;AA5EC;EACC;EACA;AA8EF;AA+BC;EACC,sBAlJM;EAmJN;AA7BF;AA+BC;EACC;EACA;AA7BF;AAgCC;EACC;EACA;AA9BF;;AAmCA;EACC;AAhCD;AAkCC;EAvHA;EACA;EACA;EACA;EAsHC;EACA;EACA;AA7BF;AA+BE;EACC;EACA;EACA;AA7BH;;AAmCA;EACC;EACA;AAhCD;AAkCC;EACC,cA7LY;EA8LZ;AAhCF;AAmCC;EACC;EACA,WA3LU;AA0JZ;;AAsCA;EACC;EACA;AAnCD;AAqCC;EACC;AAnCF;AAsCC;EACC;AApCF;AAuCC;EACC;EACA;EACA;EACA;EACA;EACA;EACA,cAzNW;AAoLb;AAuCE;EACC,cAjOa;AA4LhB;;AA2CA;EACC;EACA;EACA;AAxCD;AA0CC;EACC;AAxCF;AA2CC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAzCF;AA2CE;EACC,yBArPU;AA4Mb;AA4CE;EACC;EACA,WA3PK;AAiNR;AA8CC;EACC;AA5CF;AA+CC;EACC,yBAlQY;EAmQZ,WAlQU;EAmQV;EACA;EACA;EACA;AA7CF;AA+CE;EACC;EACA,WA7QK;AAgOR;;AAoDC;EACC;EACA;EACA;AAjDF;AAoDC;EACC;EACA;EACA;AAlDF;AAqDC;EACC;EACA;EACA,WAhSU;EAiSV;AAnDF;;AAwDA;EACC,gBA1SO;EA2SP;EACA;EACA;EACA;EArQA;AAiND;AAuDC;EACC;AArDF;AAyDE;EACC;EACA;EACA;AAvDH;AA0DE;EACC;AAxDH;AA4DC;EACC;EACA;AA1DF;AA4DE;EACC;EACA;AA1DH;AA6DE;EACC;EACA;EACA;AA3DH;AA8DE;EACC;EACA;EACA;EACA;EACA;AA5DH;AA+DE;EACC;AA7DH;AAiEC;EACC;EACA;EACA;KAAA;EACA;AA/DF;AAiEE;EACC;AA/DH;AAmEC;EAEC;EACA;EACA;EACA;EACA,yBA7WW;EA8WX,WA5WU;EA6WV;EACA;AAlEF;AAoEE;EACC;AAlEH;AAsEC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AApEF;AAsEE;EACC;AApEH;AAwEC;EACC;EACA;AAtEF;AAwEE;EACC,gBA/YK;AAyUR;AAwEG;EACC;AAtEJ;AA2EC;EACC;EACA;EACA;EACA,cA9ZW;AAqVb;AA4EC;EACC;EACA,cAxac;EAyad;EACA;EACA;EACA;AA1EF;AA6EC;EACC;EACA,WAtaU;EAuaV;EACA;AA3EF;;AAgFA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AA7ED;;AAgFA;EACC,gBA/bO;EAgcP;EACA;EACA;EACA;EACA;EACA;EACA;EA7ZA;AAiVD;AA+EC;EACC;EACA;EACA;EACA;EACA;EACA,mBA9cW;AAiYb;AAgFC;EACC;EACA;EACA;EACA;EACA;EACA;AA9EF;AAgFE;EACC,cAnea;AAqZhB;AAkFC;EACC;EACA;EACA;EACA;EACA;EACA,WAleU;AAkZZ;AAkFE;EACC,yBAteW;AAsZd;AAmFE;EACC;AAjFH;AAqFC;EACC;EACA;EACA;AAnFF;AAsFC;EACC;EACA;EACA;EACA;EACA;EACA,mBA3fW;AAuab;;AAyFA;EACC;AAtFD;;AAyFA;EACC;EACA;EACA;EACA,cA5gBY;AAsbb;;AAyFA;;EAEC;EACA;EACA;EACA;EACA;EACA;EACA;AAtFD;AAwFC;;EACC;EACA,qBAhiBc;EAiiBd;AArFF;;AAyFA;EACC;EACA;EACA,WA7hBW;AAucZ;;AA0FA;EACC;EACA;EACA;AAvFD;AAyFC;EACC;AAvFF;AA0FC;EACC;EACA;EACA;AAxFF;;AA8FC;EACC;AA3FF;AA6FE;EACC;EACA;AA3FH;AA+FC;EACC,mBA/jBW;EAgkBX;EACA;AA7FF;;AAiGA;EACC;EACA;AA9FD;AAgGC;EACC;EACA;EACA,cAhlBW;AAkfb;;AAkGA;EACC;AA/FD;AAiGC;EACC;EACA;EACA,mBArlBY;EAslBZ;EACA;AA/FF;AAkGC;EACC;EACA;EACA;EACA;AAhGF;AAmGC;EACC;EACA;EACA;EACA,cA1mBW;AAygBb;;AAqGA;EACC;EACA;EACA;AAlGD;AAoGC;EACC;EACA,cAxnBc;EAynBd;AAlGF;AAoGE;EACC;AAlGH;AAsGC;EACC;EACA;EACA,cAhoBW;AA4hBb;AAuGC;EACC;EACA,WA/nBU;AA0hBZ;AAwGC;EACC;EACA;EACA;AAtGF;;AA0GA;EACC;EACA;AAvGD;AAyGC;EACC;EACA;AAvGF;AA0GC;EACC;EACA,cA5pBY;EA6pBZ;AAxGF;AA2GC;EACC;EACA,WA1pBU;AAijBZ;;AA8GA;EACC;AA3GD;;AA8GA;EACC;AA3GD;AA6GC;EACC;EACA;EACA,cA/qBW;AAokBb;;AAgHC;EACC;EACA;EACA,WAjrBU;AAokBZ;AAgHC;EACC;EACA;EACA;EACA;AA9GF;;AAkHA;EACC;EACA;EACA;EACA;EACA;EACA;AA/GD;AAiHC;EACC;AA/GF;AAkHC;EACC,yBA3sBW;AA2lBb;AAmHC;EACC;EACA,qBAztBc;AAwmBhB;AAoHC;EACC;AAlHF;AAqHC;EACC;EACA;EACA;EACA,cA/tBW;AA4mBb;AAsHC;EACC;EACA;EACA;EACA,WAhuBU;AA4mBZ;AAuHC;EAEC;AAtHF;AAyHC;EACC;AAvHF;AA0HC;EACC,WA7uBU;EA8uBV;EACA;EACA;EACA;AAxHF;AA0HE;EACC,yBArvBW;EAsvBX,cAhwBa;AAwoBhB;;AA6HA;EACC;EACA;AA1HD;AA4HC;EACC;EACA,cAzwBc;EA0wBd;AA1HF;AA4HE;EACC;AA1HH;AA8HC;EACC,cAlxBc;EAmxBd;AA5HF;;AAgIA;EACC;EACA,cAxxBa;EAyxBb;EACA;EACA;EACA;EACA;EACA;AA7HD;AA+HC;EACC;AA7HF;;AAkIA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;AA/HD;AAiIC;EACC;EACA;EACA;EACA;EACA;AA/HF;;AAmIA;EACC;IACC;IACA;EAhIA;EAkID;IACC;IACA;EAhIA;AACF;AAoIA;EACC;IACC;EAlIA;EAoIA;IACC;IACA;IACA;EAlID;EAqIA;IACC;IACA;EAnID;EAsIA;IACC;IACA;EApID;EAwID;IACC;EAtIA;EA0IA;IACC;IACA;EAxID;EA6IA;IACC;EA3ID;EA6IC;IACC;EA3IF;EA8IC;IACC;IACA;IACA;EA5IF;EA+IC;IACC;EA7IF;EAkJD;IACC;IACA;EAhJA;EAkJA;IACC;EAhJD;EAmJA;IACC;IACA;IACA;EAjJD;EAmJC;IACC;IACA;EAjJF;EAsJD;IACC;IACA;EApJA;EAsJA;IACC;IACA;EApJD;AACF,C", "sources": ["webpack://divi-layout-library/./react_app/index.scss"], "sourcesContent": ["// Divi Layout Library Styles\n// Following BEM naming convention and WordPress admin styling\n\n// Variables\n$primary-color: #0073aa;\n$secondary-color: #00a0d2;\n$success-color: #46b450;\n$error-color: #dc3232;\n$warning-color: #ffb900;\n$text-color: #23282d;\n$border-color: #ddd;\n$background-color: #f1f1f1;\n$white: #fff;\n$gray-light: #f9f9f9;\n$gray-medium: #e5e5e5;\n$gray-dark: #666;\n\n// Mixins\n@mixin button-style($bg-color, $text-color: $white) {\n\tbackground-color: $bg-color;\n\tcolor: $text-color;\n\tborder: 1px solid darken($bg-color, 10%);\n\tpadding: 8px 16px;\n\tborder-radius: 3px;\n\tcursor: pointer;\n\ttext-decoration: none;\n\tdisplay: inline-flex;\n\talign-items: center;\n\tgap: 5px;\n\tfont-size: 13px;\n\tline-height: 1.4;\n\ttransition: all 0.2s ease;\n\n\t&:hover {\n\t\tbackground-color: darken($bg-color, 5%);\n\t\tborder-color: darken($bg-color, 15%);\n\t\tcolor: $text-color;\n\t}\n\n\t&:focus {\n\t\tbox-shadow:\n\t\t\t0 0 0 1px $white,\n\t\t\t0 0 0 3px $bg-color;\n\t\toutline: none;\n\t}\n\n\t&:disabled {\n\t\topacity: 0.6;\n\t\tcursor: not-allowed;\n\t}\n}\n\n@mixin card-shadow {\n\tbox-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n@mixin loading-spinner {\n\tborder: 2px solid $gray-medium;\n\tborder-top: 2px solid $primary-color;\n\tborder-radius: 50%;\n\tanimation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n\t0% {\n\t\ttransform: rotate(0deg);\n\t}\n\t100% {\n\t\ttransform: rotate(360deg);\n\t}\n}\n\n// Main App Container\n.dll-app {\n\tfont-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto,\n\t\tOxygen-Sans, Ubuntu, Cantarell, \"Helvetica Neue\", sans-serif;\n\tcolor: $text-color;\n\tline-height: 1.4;\n}\n\n// Dashboard\n.dll-dashboard {\n\tpadding: 20px;\n\n\t&--loading,\n\t&--error {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmin-height: 400px;\n\t}\n\n\t&__header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 30px;\n\t\tpadding-bottom: 20px;\n\t\tborder-bottom: 1px solid $border-color;\n\t}\n\n\t&__title {\n\t\tmargin: 0;\n\t\tfont-size: 24px;\n\t\tfont-weight: 600;\n\t\tcolor: $text-color;\n\t}\n\n\t&__toolbar {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 15px;\n\t}\n\n\t&__content {\n\t\tdisplay: flex;\n\t\tgap: 30px;\n\t}\n\n\t&__main {\n\t\tflex: 1;\n\t}\n}\n\n// View Toggle\n.dll-view-toggle {\n\tdisplay: flex;\n\tborder-radius: 3px;\n\toverflow: hidden;\n\n\t&__button {\n\t\tbackground: $white;\n\t\tborder: none;\n\t\tpadding: 8px 12px;\n\t\tcursor: pointer;\n\t\tcolor: $gray-dark;\n\t\ttransition: all 0.2s ease;\n\n\t\t&--active {\n\t\t\tbackground-color: $primary-color;\n\t\t\tcolor: $white;\n\t\t}\n\n\t\t.dashicons {\n\t\t\tfont-size: 16px;\n\t\t}\n\t}\n}\n\n// Buttons\n.dll-button {\n\t@include button-style($primary-color);\n\n\t&--secondary {\n\t\t@include button-style($white, $text-color);\n\t}\n\n\t&.button-secondary {\n\t\tbackground-color: $white;\n\t\tborder-color: transparent;\n\t}\n\t&--small {\n\t\tpadding: 6px 12px;\n\t\tfont-size: 12px;\n\t}\n\n\t.dashicons {\n\t\tfont-size: 14px;\n\t\tline-height: 1.4rem;\n\t}\n}\n\n// Loading\n.dll-loading {\n\ttext-align: center;\n\n\t&__spinner {\n\t\t@include loading-spinner;\n\t\twidth: 40px;\n\t\theight: 40px;\n\t\tmargin: 0 auto 15px;\n\n\t\t&--small {\n\t\t\twidth: 16px;\n\t\t\theight: 16px;\n\t\t\tborder-width: 1px;\n\t\t}\n\t}\n}\n\n// Error\n.dll-error {\n\ttext-align: center;\n\tpadding: 40px 20px;\n\n\th3 {\n\t\tcolor: $error-color;\n\t\tmargin-bottom: 10px;\n\t}\n\n\tp {\n\t\tmargin-bottom: 20px;\n\t\tcolor: $gray-dark;\n\t}\n}\n\n// Sidebar\n.dll-sidebar {\n\twidth: 250px;\n\tflex-shrink: 0;\n\n\t&__header {\n\t\tmargin-bottom: 20px;\n\t}\n\n\t&__search {\n\t\tmargin-bottom: 10px;\n\t}\n\n\t&__title {\n\t\tfont-size: 16px;\n\t\tfont-weight: 600;\n\t\tmargin: 0;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 8px;\n\t\tcolor: $text-color;\n\n\t\t.dashicons {\n\t\t\tcolor: $primary-color;\n\t\t}\n\t}\n}\n\n// Category List\n.dll-category-list {\n\tlist-style: none;\n\tmargin: 0;\n\tpadding: 0;\n\n\t&__item {\n\t\tmargin-bottom: 2px;\n\t}\n\n\t&__button {\n\t\twidth: 100%;\n\t\tbackground: none;\n\t\tborder: none;\n\t\tpadding: 10px 15px;\n\t\ttext-align: left;\n\t\tcursor: pointer;\n\t\tborder-radius: 3px;\n\t\ttransition: all 0.2s ease;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\n\t\t&:hover {\n\t\t\tbackground-color: $gray-light;\n\t\t}\n\n\t\t&--active {\n\t\t\tbackground-color: $primary-color !important;\n\t\t\tcolor: $white;\n\t\t}\n\t}\n\n\t&__name {\n\t\tfont-weight: 500;\n\t}\n\n\t&__count {\n\t\tbackground-color: $gray-medium;\n\t\tcolor: $gray-dark;\n\t\tpadding: 2px 6px;\n\t\tborder-radius: 10px;\n\t\tfont-size: 11px;\n\t\tfont-weight: 600;\n\n\t\t.dll-category-list__button--active & {\n\t\t\tbackground-color: rgba(255, 255, 255, 0.2);\n\t\t\tcolor: $white;\n\t\t}\n\t}\n}\n\n// Layouts Grid/List\n.dll-layouts {\n\t&--grid {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n\t\tgap: 20px;\n\t}\n\n\t&--list {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 15px;\n\t}\n\n\t&__empty {\n\t\ttext-align: center;\n\t\tpadding: 60px 20px;\n\t\tcolor: $gray-dark;\n\t\tgrid-column: 1 / -1;\n\t}\n}\n\n// Layout Card\n.dll-layout-card {\n\tbackground: $white;\n\tborder: 1px solid $border-color;\n\tborder-radius: 6px;\n\toverflow: hidden;\n\ttransition: all 0.3s ease;\n\t@include card-shadow;\n\n\t&:hover {\n\t\tbox-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n\t}\n\n\t&--grid {\n\t\t.dll-layout-card__image-container {\n\t\t\theight: 200px;\n\t\t\tposition: relative;\n\t\t\toverflow: hidden;\n\t\t}\n\n\t\t.dll-layout-card__content {\n\t\t\tpadding: 15px;\n\t\t}\n\t}\n\n\t&--list {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\n\t\t.dll-layout-card__image-wrapper {\n\t\t\twidth: 150px;\n\t\t\tflex-shrink: 0;\n\t\t}\n\n\t\t.dll-layout-card__image-container {\n\t\t\theight: 100px;\n\t\t\tposition: relative;\n\t\t\toverflow: hidden;\n\t\t}\n\n\t\t.dll-layout-card__content-wrapper {\n\t\t\tflex: 1;\n\t\t\tpadding: 15px;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t.dll-layout-card__actions-list {\n\t\t\tmargin-left: 20px;\n\t\t}\n\t}\n\n\t&__image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t\ttransition: transform 0.3s ease;\n\n\t\t&--scrolling {\n\t\t\ttransform: translateY(-20%);\n\t\t}\n\t}\n\n\t&__image-placeholder,\n\t&__image-error {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\theight: 100%;\n\t\tbackground-color: $gray-light;\n\t\tcolor: $gray-dark;\n\t\tflex-direction: column;\n\t\tgap: 10px;\n\n\t\t.dashicons {\n\t\t\tfont-size: 24px;\n\t\t}\n\t}\n\n\t&__overlay {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: rgba(0, 0, 0, 0.7);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\topacity: 0;\n\t\ttransition: opacity 0.3s ease;\n\n\t\t.dll-layout-card:hover & {\n\t\t\topacity: 1;\n\t\t}\n\t}\n\n\t&__actions {\n\t\tdisplay: flex;\n\t\tgap: 10px;\n\n\t\t> button.is-secondary {\n\t\t\tbackground: $white;\n\n\t\t\t&:hover {\n\t\t\t\tbackground: $white !important;\n\t\t\t}\n\t\t}\n\t}\n\n\t&__title {\n\t\tfont-size: 16px;\n\t\tfont-weight: 600;\n\t\tmargin: 0 0 5px 0;\n\t\tcolor: $text-color;\n\t}\n\n\t&__category {\n\t\tfont-size: 12px;\n\t\tcolor: $primary-color;\n\t\tfont-weight: 500;\n\t\tmargin: 0 0 8px 0;\n\t\ttext-transform: uppercase;\n\t\tletter-spacing: 0.5px;\n\t}\n\n\t&__description {\n\t\tfont-size: 13px;\n\t\tcolor: $gray-dark;\n\t\tmargin: 0;\n\t\tline-height: 1.4;\n\t}\n}\n\n// Modal\n.dll-modal-overlay {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: rgba(0, 0, 0, 0.5);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tz-index: 100000;\n\tpadding: 20px;\n}\n\n.dll-modal {\n\tbackground: $white;\n\tborder-radius: 6px;\n\tmax-width: 600px;\n\twidth: 100%;\n\tmax-height: 90vh;\n\toverflow: hidden;\n\tdisplay: flex;\n\tflex-direction: column;\n\t@include card-shadow;\n\n\t&__header {\n\t\tpadding: 20px;\n\t\tborder-bottom: 1px solid $border-color;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tbackground: $gray-light;\n\t}\n\n\t&__title {\n\t\tmargin: 0;\n\t\tfont-size: 18px;\n\t\tfont-weight: 600;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 8px;\n\n\t\t.dashicons {\n\t\t\tcolor: $primary-color;\n\t\t}\n\t}\n\n\t&__close {\n\t\tbackground: none;\n\t\tborder: none;\n\t\tpadding: 5px;\n\t\tcursor: pointer;\n\t\tborder-radius: 3px;\n\t\tcolor: $gray-dark;\n\n\t\t&:hover {\n\t\t\tbackground-color: $gray-medium;\n\t\t}\n\n\t\t.dashicons {\n\t\t\tfont-size: 18px;\n\t\t}\n\t}\n\n\t&__content {\n\t\tpadding: 20px;\n\t\toverflow-y: auto;\n\t\tflex: 1;\n\t}\n\n\t&__footer {\n\t\tpadding: 20px;\n\t\tborder-top: 1px solid $border-color;\n\t\tdisplay: flex;\n\t\tjustify-content: flex-end;\n\t\tgap: 10px;\n\t\tbackground: $gray-light;\n\t}\n}\n\n// Forms\n.dll-form-group {\n\tmargin-bottom: 20px;\n}\n\n.dll-form-label {\n\tdisplay: block;\n\tmargin-bottom: 5px;\n\tfont-weight: 500;\n\tcolor: $text-color;\n}\n\n.dll-form-input,\n.dll-form-select {\n\twidth: 100%;\n\tmax-width: 100% !important;\n\tpadding: 8px 12px;\n\tborder: 1px solid $border-color;\n\tborder-radius: 3px;\n\tfont-size: 14px;\n\ttransition: border-color 0.2s ease;\n\n\t&:focus {\n\t\toutline: none;\n\t\tborder-color: $primary-color;\n\t\tbox-shadow: 0 0 0 1px $primary-color;\n\t}\n}\n\n.dll-form-help {\n\tmargin-top: 5px;\n\tfont-size: 12px;\n\tcolor: $gray-dark;\n}\n\n// Radio Options\n.dll-radio-option {\n\tdisplay: block;\n\tmargin-bottom: 15px;\n\tcursor: pointer;\n\n\tinput[type=\"radio\"] {\n\t\tmargin-right: 10px;\n\t}\n\n\t&__label {\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\t\tgap: 10px;\n\t}\n}\n\n// Import Modal Specific\n.dll-import-modal {\n\t.dll-import-options {\n\t\tmargin-bottom: 25px;\n\n\t\th3 {\n\t\t\tmargin-bottom: 15px;\n\t\t\tfont-size: 16px;\n\t\t}\n\t}\n\n\t.dll-page-options {\n\t\tbackground: $gray-light;\n\t\tborder-radius: 3px;\n\t\tmargin-top: 15px;\n\t}\n}\n\n.dll-import-progress {\n\ttext-align: center;\n\tpadding: 40px 20px;\n\n\t&__text {\n\t\tmargin-bottom: 20px;\n\t\tfont-size: 16px;\n\t\tcolor: $text-color;\n\t}\n}\n\n.dll-progress {\n\tmargin-bottom: 15px;\n\n\t&__bar {\n\t\twidth: 100%;\n\t\theight: 8px;\n\t\tbackground: $gray-medium;\n\t\tborder-radius: 4px;\n\t\toverflow: hidden;\n\t}\n\n\t&__fill {\n\t\theight: 100%;\n\t\tbackground: linear-gradient(90deg, $primary-color, $secondary-color);\n\t\ttransition: width 0.3s ease;\n\t\tborder-radius: 4px;\n\t}\n\n\t&__text {\n\t\tmargin-top: 10px;\n\t\tfont-size: 14px;\n\t\tfont-weight: 500;\n\t\tcolor: $text-color;\n\t}\n}\n\n.dll-import-success {\n\ttext-align: center;\n\tpadding: 40px 20px;\n\tposition: relative;\n\n\t&__icon {\n\t\tfont-size: 48px;\n\t\tcolor: $success-color;\n\t\tmargin-bottom: 15px;\n\n\t\t.dashicons {\n\t\t\tfont-size: 48px;\n\t\t}\n\t}\n\n\t&__title {\n\t\tfont-size: 20px;\n\t\tmargin-bottom: 10px;\n\t\tcolor: $text-color;\n\t}\n\n\t&__message {\n\t\tmargin-bottom: 25px;\n\t\tcolor: $gray-dark;\n\t}\n\n\t&__actions {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tgap: 10px;\n\t}\n}\n\n.dll-import-error {\n\ttext-align: center;\n\tpadding: 40px 20px;\n\n\t&__icon {\n\t\tfont-size: 48px;\n\t\tmargin-bottom: 15px;\n\t}\n\n\t&__title {\n\t\tfont-size: 18px;\n\t\tcolor: $error-color;\n\t\tmargin-bottom: 10px;\n\t}\n\n\t&__message {\n\t\tmargin-bottom: 25px;\n\t\tcolor: $gray-dark;\n\t}\n}\n\n// Export Modal Specific\n.dll-export-modal {\n\tmax-width: 700px;\n}\n\n.dll-export-step {\n\tmargin-bottom: 30px;\n\n\th3 {\n\t\tmargin-bottom: 15px;\n\t\tfont-size: 16px;\n\t\tcolor: $text-color;\n\t}\n}\n\n.dll-layout-list {\n\t&__empty {\n\t\ttext-align: center;\n\t\tpadding: 40px 20px;\n\t\tcolor: $gray-dark;\n\t}\n\n\t&__items {\n\t\tmax-height: 300px;\n\t\toverflow-y: auto;\n\t\tborder: 1px solid $border-color;\n\t\tborder-radius: 3px;\n\t}\n}\n\n.dll-layout-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 15px;\n\tborder-bottom: 1px solid $border-color;\n\tcursor: pointer;\n\ttransition: background-color 0.2s ease;\n\n\t&:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\t&:hover {\n\t\tbackground-color: $gray-light;\n\t}\n\n\t&--selected {\n\t\tbackground-color: lighten($primary-color, 45%);\n\t\tborder-color: $primary-color;\n\t}\n\n\t&__content {\n\t\tflex: 1;\n\t}\n\n\t&__title {\n\t\tmargin: 0 0 5px 0;\n\t\tfont-size: 14px;\n\t\tfont-weight: 600;\n\t\tcolor: $text-color;\n\t}\n\n\t&__meta {\n\t\tdisplay: flex;\n\t\tgap: 15px;\n\t\tfont-size: 12px;\n\t\tcolor: $gray-dark;\n\t}\n\n\t&__type,\n\t&__status {\n\t\ttext-transform: capitalize;\n\t}\n\n\t&__actions {\n\t\tmargin-left: 15px;\n\t}\n\n\t&__edit {\n\t\tcolor: $gray-dark;\n\t\ttext-decoration: none;\n\t\tpadding: 5px;\n\t\tborder-radius: 3px;\n\t\ttransition: background-color 0.2s ease;\n\n\t\t&:hover {\n\t\t\tbackground-color: $gray-medium;\n\t\t\tcolor: $primary-color;\n\t\t}\n\t}\n}\n\n.dll-export-success {\n\ttext-align: center;\n\tpadding: 40px 20px;\n\n\t&__icon {\n\t\tfont-size: 48px;\n\t\tcolor: $success-color;\n\t\tmargin-bottom: 15px;\n\n\t\t.dashicons {\n\t\t\tfont-size: 48px;\n\t\t}\n\t}\n\n\th3 {\n\t\tcolor: $success-color;\n\t\tmargin-bottom: 10px;\n\t}\n}\n\n.dll-export-error-inline {\n\tbackground-color: lighten($error-color, 45%);\n\tcolor: $error-color;\n\tpadding: 10px 15px;\n\tborder-radius: 3px;\n\tmargin-top: 15px;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8px;\n\n\t.dashicons {\n\t\tflex-shrink: 0;\n\t}\n}\n\n// Confetti Animation\n.dll-confetti {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tpointer-events: none;\n\toverflow: hidden;\n\n\t&__piece {\n\t\tposition: absolute;\n\t\twidth: 8px;\n\t\theight: 8px;\n\t\ttop: -10px;\n\t\tanimation: confetti-fall 3s linear infinite;\n\t}\n}\n\n@keyframes confetti-fall {\n\t0% {\n\t\ttransform: translateY(-100vh) rotate(0deg);\n\t\topacity: 1;\n\t}\n\t100% {\n\t\ttransform: translateY(100vh) rotate(360deg);\n\t\topacity: 0;\n\t}\n}\n\n// Responsive Design\n@media (max-width: 768px) {\n\t.dll-dashboard {\n\t\tpadding: 15px;\n\n\t\t&__header {\n\t\t\tflex-direction: column;\n\t\t\talign-items: flex-start;\n\t\t\tgap: 15px;\n\t\t}\n\n\t\t&__toolbar {\n\t\t\twidth: 100%;\n\t\t\tjustify-content: space-between;\n\t\t}\n\n\t\t&__content {\n\t\t\tflex-direction: column;\n\t\t\tgap: 20px;\n\t\t}\n\t}\n\n\t.dll-sidebar {\n\t\twidth: 100%;\n\t}\n\n\t.dll-layouts {\n\t\t&--grid {\n\t\t\tgrid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n\t\t\tgap: 15px;\n\t\t}\n\t}\n\n\t.dll-layout-card {\n\t\t&--list {\n\t\t\tflex-direction: column;\n\n\t\t\t.dll-layout-card__image-wrapper {\n\t\t\t\twidth: 100%;\n\t\t\t}\n\n\t\t\t.dll-layout-card__content {\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: flex-start;\n\t\t\t\tgap: 15px;\n\t\t\t}\n\n\t\t\t.dll-layout-card__actions-list {\n\t\t\t\tmargin-left: 0;\n\t\t\t}\n\t\t}\n\t}\n\n\t.dll-modal {\n\t\tmargin: 10px;\n\t\tmax-height: calc(100vh - 20px);\n\n\t\t&__content {\n\t\t\tpadding: 15px;\n\t\t}\n\n\t\t&__footer {\n\t\t\tpadding: 15px;\n\t\t\tflex-direction: column;\n\t\t\tgap: 10px;\n\n\t\t\t.dll-button {\n\t\t\t\twidth: 100%;\n\t\t\t\tjustify-content: center;\n\t\t\t}\n\t\t}\n\t}\n\n\t.dll-import-success__actions {\n\t\tflex-direction: column;\n\t\tgap: 10px;\n\n\t\t.dll-button {\n\t\t\twidth: 100%;\n\t\t\tjustify-content: center;\n\t\t}\n\t}\n}\n"], "names": [], "sourceRoot": ""}