<?php
/**
 * Admin class
 *
 * @since 1.0.0
 */

namespace DiviLayoutLibrary;

if ( ! defined( 'ABSPATH' ) ) {
	exit();
}


use DiviLayoutLibrary\Admin\Menu;
use DiviLayoutLibrary\Admin\Assets;
use DiviLayoutLibrary\Admin\Ajax;

/**
 * Admin class
 *
 * @since 1.0.0
 */
class Admin {
	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */
	public function __construct() {
		$this->add_menu();
		$this->add_assets();
		$this->add_actions();
	}

	/**
	 * Add admin menu page
	 *
	 * @since 1.0.0
	 */
	private function add_menu() {
		new Menu();
	}

	/**
	 * Add admin assets
	 *
	 * @since 1.0.0
	 */
	private function add_assets() {
		new Assets();
	}

	/**
	 * Add admin actions
	 *
	 * @since 1.0.0
	 */
	public function add_actions() {
		add_filter( 'et_core_portability_paginate_images', '__return_false', 99 );
		new Ajax();
	}
}
