<?php
/**
 * Menu class
 *
 * @since 1.0.0
 * @package DiviLayoutLibrary
 */

namespace DiviLayoutLibrary\Admin;

if ( ! defined( 'ABSPATH' ) ) {
	exit();
}

/**
 * Menu class
 *
 * @since 1.0.0
 */
class Menu {
	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */
	public function __construct() {
		add_action( 'admin_menu', array( $this, 'admin_menu' ) );
	}

	/**
	 * Add admin menu page
	 *
	 * @return void
	 *
	 * @since 1.0.0
	 */
	public function admin_menu() {
		add_submenu_page(
			'et_divi_options',
			__( 'Divi Import Layouts', 'divi-layout-library' ),
			__( 'Divi Import Layouts', 'divi-layout-library' ),
			'manage_options',
			'divi-layout-library',
			array( $this, 'load_main_template' )
		);
	}

	/**
	 * Load main template in dashboard for react.js integration
	 *
	 * @return void
	 *
	 * @since 1.0.0
	 */
	public function load_main_template() {
		echo '<div id="divi-layout-library-body" class="divi-layout-library-body"></div>';
	}
}
