<?php
/**
 * Assets class
 *
 * @since 1.0.0
 * @package DiviLayoutLibrary
 */

namespace DiviLayoutLibrary\Admin;

if ( ! defined( 'ABSPATH' ) ) {
	exit();
}

/**
 * Assets class
 *
 * @since 1.0.0
 */
class Assets {
	/**
	 * Pages to enqueue scripts
	 *
	 * @since 1.0.0
	 * @var array
	 */
	private $pages = array( 'divi_page_divi-layout-library' );

	/**
	 * Constructor
	 *
	 * @since 1.0.0
	 */
	public function __construct() {
		add_action( 'admin_enqueue_scripts', array( $this, 'plugin_scripts' ) );
	}

	/**
	 * Enqueue plugin scripts
	 *
	 * @param string $hook The current page hook.
	 *
	 * @since 1.0.0
	 */
	public function plugin_scripts( $hook ) {
		if ( ! in_array( $hook, $this->pages, true ) ) {
			return;
		}

		$dependencies_file = DIVI_LAYOUT_LIBARY_ASSETS_DIR_PATH . 'js/divi-layout-library.core.min.asset.php';

		if ( ! file_exists( $dependencies_file ) ) {
			return;
		}

		$dependencies = include_once $dependencies_file;
		
		if ( function_exists( 'et_core_load_component' ) ) {
			et_core_load_component( array( 'portability' ) );
		}
		
		wp_enqueue_style( 'divi-layout-library', DIVI_LAYOUT_LIBARY_ASSETS_URI . 'js/divi-layout-library.core.min.css', array('wp-components'), $dependencies['version'], 'all' );
		wp_enqueue_script(
			'divi-layout-library',
			DIVI_LAYOUT_LIBARY_ASSETS_URI . 'js/divi-layout-library.core.min.js',
			array_merge( $dependencies['dependencies'], array( 'regenerator-runtime', 'jquery' ) ),
			$dependencies['version'],
			true
		);

		wp_localize_script(
			'divi-layout-library',
			'dllAjax',
			array(
				'ajaxUrl'           => admin_url( 'admin-ajax.php' ),
				'nonce'             => wp_create_nonce( 'dll_nonce' ),
				'portability_nonce' => wp_create_nonce( 'et_core_portability_import' ),
				'plugin_root_url'   => DIVI_LAYOUT_LIBARY_ROOT_DIR_URL,
				'strings'           => array(
					'importing'        => __( 'Importing layout...', 'divi-layout-library' ),
					'exporting'        => __( 'Exporting layout...', 'divi-layout-library' ),
					'success'          => __( 'Success!', 'divi-layout-library' ),
					'error'            => __( 'An error occurred', 'divi-layout-library' ),
					'invalidFile'      => __( 'Invalid file format', 'divi-layout-library' ),
					'confirmExport'    => __( 'Are you sure you want to export this layout?', 'divi-layout-library' ),
					'selectLayout'     => __( 'Please select a layout to export', 'divi-layout-library' ),
					'pageNameRequired' => __( 'Page name is required', 'divi-layout-library' ),
				),
			)
		);
	}
}
