<?php
/**
 * Ajax class
 *
 * @since 1.0.0
 * @package DiviLayoutLibrary
 */

namespace DiviLayoutLibrary\Admin;

if ( ! defined( 'ABSPATH' ) ) {
	exit();
}

/**
 * Ajax class
 *
 * @since 1.0.0
 */
class Ajax {
	/**
	 * Constructor - Register AJAX actions
	 */
	public function __construct() {
		add_action( 'wp_ajax_dll_create_page_with_imported_layout', array( $this, 'create_page_with_imported_layout' ) );
	}

	/**
	 * Create page with imported layout AJAX handler
	 *
	 * @since 1.0.0
	 */
	public function create_page_with_imported_layout() {
		check_ajax_referer( 'dll_nonce', 'nonce' );

		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( array( 'message' => 'Insufficient permissions' ) );
		}

		$layout_id = isset( $_POST['id'] ) ? intval( $_POST['id'] ) : null;
		if ( empty( $layout_id ) ) {
			wp_send_json_error( array( 'message' => 'Invalid layout ID' ) );
		}

		$page_title = isset( $_POST['page_title'] ) ? sanitize_text_field( wp_unslash( $_POST['page_title'] ) ) : null;
		if ( empty( $page_title ) ) {
			wp_send_json_error( array( 'message' => 'Page title is required' ) );
		}

		$page_status    = isset( $_POST['page_status'] ) ? sanitize_key( $_POST['page_status'] ) : 'draft';
		$valid_statuses = array( 'draft', 'publish', 'private', 'pending' );
		if ( ! in_array( $page_status, $valid_statuses, true ) ) {
			$page_status = 'draft';
		}

		$layout_content = get_post_field( 'post_content', $layout_id );

		if ( empty( $layout_content ) ) {
			wp_send_json_error( array( 'message' => 'No content found for the layout' ) );
		}

		// Create post object.
		$args = array(
			'post_type'    => 'page',
			'post_title'   => $page_title,
			'post_content' => $layout_content,
			'post_status'  => $page_status,
			'meta_input'   => array(
				'_et_pb_use_builder' => 'on',
			),
		);

		// Insert the post into the database.
		$post_id = wp_insert_post( $args );
		if ( is_wp_error( $post_id ) ) {
			wp_send_json_error(
				array(
					'message' => 'Failed to create page: ' . $post_id->get_error_message(),
				)
			);
		}

		$post_permalink = get_permalink( $post_id );
		wp_send_json_success(
			array(
				'message' => __( 'Page created successfully', 'divi-layout-library' ),
				'data'    => array(
					'page_id'     => $post_id,
					'page_title'  => $page_title,
					'page_status' => $page_status,
					// 'edit_url'    => admin_url( 'post.php?post=' . $post_id . '&action=edit' ),
					'edit_url'    => $post_permalink . '?et_fb=1&PageSpeed=off',
					'view_url'    => $post_permalink,
				),
			)
		);
	}
}
