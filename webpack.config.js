const defaultConfig = require("@wordpress/scripts/config/webpack.config");
const { CleanWebpackPlugin } = require("clean-webpack-plugin");
const path = require("path");

const config = {
	...defaultConfig,
	entry: {
		"divi-layout-library.core.min": path.resolve(
			__dirname,
			"react_app/index.js",
		),
	},
	output: {
		path: path.resolve(__dirname, "assets/js"),
		filename: "[name].js",
	},
	plugins: [...defaultConfig.plugins, new CleanWebpackPlugin()],
	externals: {
		// Third party dependencies.
		jquery: "jQuery",
		react: ["vendor", "React"],
		"react-dom": ["vendor", "ReactDOM"],

		// WordPress dependencies.
		"@wordpress/i18n": ["vendor", "wp", "i18n"],
		"@wordpress/hooks": ["vendor", "wp", "hooks"],
		"@wordpress/api-fetch": ["vendor", "wp", "apiFetch"],
		"@wordpress/icons": ["vendor", "wp", "icons"],
		"@wordpress/components": ["vendor", "wp", "components"],
	},
	resolve: {
		extensions: [".js", ".jsx", ".json", ".ts", ".tsx"],
		alias: {
			"@components": path.resolve(__dirname, "react_app/components/"),
			"@utils": path.resolve(__dirname, "react_app/utils/"),
			"@services": path.resolve(__dirname, "react_app/services/"),
			"@contexts": path.resolve(__dirname, "react_app/contexts/"),
		},
	},
};

module.exports = config;
